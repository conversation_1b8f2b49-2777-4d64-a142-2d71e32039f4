#!/usr/bin/env tsx
/**
 * Refactoring Validation Script
 * 
 * Comprehensive validation of all refactoring phases to ensure:
 * - Code organization improvements are in place
 * - Component library unification is complete
 * - tRPC architecture improvements are functional
 * - Database optimization is working
 * - Performance monitoring is active
 * - Documentation and testing are comprehensive
 */

import { existsSync, readFileSync, readdirSync, statSync } from "fs";
import { join } from "path";
import { createPrismaClient } from "../src/lib/prisma-config";
import { enhancedPerformanceMonitor } from "../src/lib/enhanced-performance-monitor";
import { databaseOptimizer } from "../src/lib/database-optimizer";

interface ValidationResult {
  phase: string;
  passed: boolean;
  issues: string[];
  recommendations: string[];
}

class RefactoringValidator {
  private results: ValidationResult[] = [];
  private rootDir = process.cwd();

  async validateAll(): Promise<ValidationResult[]> {
    console.log("🔍 Starting comprehensive refactoring validation...\n");

    await this.validatePhase1CodeOrganization();
    await this.validatePhase2ComponentLibrary();
    await this.validatePhase3TRPCArchitecture();
    await this.validatePhase4DatabaseOptimization();
    await this.validatePhase5PerformanceMonitoring();
    await this.validatePhase6DocumentationTesting();

    return this.results;
  }

  private async validatePhase1CodeOrganization(): Promise<void> {
    console.log("📁 Phase 1: Code Organization Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check file structure
    const requiredDirs = [
      "src/components/ui",
      "src/components/atoms", 
      "src/lib",
      "src/routers",
      "src/hooks",
      "src/utils",
      "src/__tests__",
      "src/test-utils",
      "docs"
    ];

    for (const dir of requiredDirs) {
      const fullPath = join(this.rootDir, dir);
      if (!existsSync(fullPath)) {
        issues.push(`Missing required directory: ${dir}`);
      }
    }

    // Check for duplicate utilities
    const utilFiles = this.getFilesInDirectory("src/utils");
    const libFiles = this.getFilesInDirectory("src/lib");
    
    const duplicatePatterns = ["db-utils", "validation", "helpers"];
    for (const pattern of duplicatePatterns) {
      const utilMatches = utilFiles.filter(f => f.includes(pattern));
      const libMatches = libFiles.filter(f => f.includes(pattern));
      
      if (utilMatches.length > 0 && libMatches.length > 0) {
        issues.push(`Potential duplicate utilities: ${pattern} found in both utils/ and lib/`);
      }
    }

    // Check import patterns
    const componentFiles = this.getFilesInDirectory("src/components", ".tsx");
    let inconsistentImports = 0;
    
    for (const file of componentFiles.slice(0, 5)) { // Sample check
      const content = this.readFileContent(file);
      if (content.includes("../../../") || content.includes("../../../../")) {
        inconsistentImports++;
      }
    }

    if (inconsistentImports > 0) {
      recommendations.push(`Found ${inconsistentImports} files with deep relative imports. Consider using absolute imports with @/ alias.`);
    }

    this.results.push({
      phase: "Phase 1: Code Organization",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} Code Organization: ${issues.length} issues found\n`);
  }

  private async validatePhase2ComponentLibrary(): Promise<void> {
    console.log("🧩 Phase 2: Component Library Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check unified button system
    const buttonPath = "src/components/ui/button.tsx";
    if (!existsSync(join(this.rootDir, buttonPath))) {
      issues.push("Unified button component not found");
    } else {
      const buttonContent = this.readFileContent(buttonPath);
      
      // Check for enhanced features
      const requiredFeatures = ["loading", "icon", "iconPosition", "ButtonProps"];
      for (const feature of requiredFeatures) {
        if (!buttonContent.includes(feature)) {
          issues.push(`Button component missing feature: ${feature}`);
        }
      }
    }

    // Check modal system
    const modalPath = "src/components/ui/modal.tsx";
    if (!existsSync(join(this.rootDir, modalPath))) {
      issues.push("Unified modal component not found");
    } else {
      const modalContent = this.readFileContent(modalPath);
      
      if (!modalContent.includes("ConfirmationModal")) {
        issues.push("Modal system missing ConfirmationModal variant");
      }
      
      if (!modalContent.includes("useModal")) {
        issues.push("Modal system missing useModal hook");
      }
    }

    // Check form system
    const formPath = "src/components/ui/form.tsx";
    if (!existsSync(join(this.rootDir, formPath))) {
      issues.push("Unified form component not found");
    } else {
      const formContent = this.readFileContent(formPath);
      
      const formComponents = ["FormField", "FormLabel", "FormControl", "FormMessage", "useForm"];
      for (const component of formComponents) {
        if (!formContent.includes(component)) {
          issues.push(`Form system missing component: ${component}`);
        }
      }
    }

    // Check loading system
    const loadingPath = "src/components/ui/loading.tsx";
    if (!existsSync(join(this.rootDir, loadingPath))) {
      issues.push("Unified loading component not found");
    } else {
      const loadingContent = this.readFileContent(loadingPath);
      
      const loadingComponents = ["Loading", "PageLoading", "InlineLoading", "SkeletonText"];
      for (const component of loadingComponents) {
        if (!loadingContent.includes(component)) {
          issues.push(`Loading system missing component: ${component}`);
        }
      }
    }

    // Check component index
    const indexPath = "src/components/ui/index.ts";
    if (!existsSync(join(this.rootDir, indexPath))) {
      recommendations.push("Consider creating a component index file for easier imports");
    }

    this.results.push({
      phase: "Phase 2: Component Library",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} Component Library: ${issues.length} issues found\n`);
  }

  private async validatePhase3TRPCArchitecture(): Promise<void> {
    console.log("🔌 Phase 3: tRPC Architecture Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check unified middleware
    const middlewarePath = "src/lib/trpc-middleware.ts";
    if (!existsSync(join(this.rootDir, middlewarePath))) {
      issues.push("Unified tRPC middleware not found");
    } else {
      const middlewareContent = this.readFileContent(middlewarePath);
      
      const requiredMiddleware = [
        "rateLimitMiddleware",
        "usageTrackingMiddleware", 
        "featureAccessMiddleware",
        "performanceMiddleware",
        "createFeatureProcedure"
      ];
      
      for (const middleware of requiredMiddleware) {
        if (!middlewareContent.includes(middleware)) {
          issues.push(`Missing middleware: ${middleware}`);
        }
      }
    }

    // Check common schemas
    const schemasPath = "src/lib/trpc-schemas.ts";
    if (!existsSync(join(this.rootDir, schemasPath))) {
      issues.push("Common tRPC schemas not found");
    } else {
      const schemasContent = this.readFileContent(schemasPath);
      
      const requiredSchemas = [
        "commonInputSchemas",
        "mentionSchemas", 
        "accountSchemas",
        "aiSchemas",
        "responseSchemas"
      ];
      
      for (const schema of requiredSchemas) {
        if (!schemasContent.includes(schema)) {
          issues.push(`Missing schema: ${schema}`);
        }
      }
    }

    // Check router utilities
    const routerUtilsPath = "src/lib/trpc-router-utils.ts";
    if (!existsSync(join(this.rootDir, routerUtilsPath))) {
      recommendations.push("Consider creating router utilities for common patterns");
    }

    // Check router migration
    const routerFiles = this.getFilesInDirectory("src/routers", ".ts");
    let migratedRouters = 0;
    
    for (const routerFile of routerFiles) {
      const content = this.readFileContent(routerFile);
      if (content.includes("createFeatureProcedure") || content.includes("createMonitoredProcedure")) {
        migratedRouters++;
      }
    }

    if (migratedRouters === 0 && routerFiles.length > 0) {
      recommendations.push("Consider migrating routers to use unified middleware system");
    }

    this.results.push({
      phase: "Phase 3: tRPC Architecture",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} tRPC Architecture: ${issues.length} issues found\n`);
  }

  private async validatePhase4DatabaseOptimization(): Promise<void> {
    console.log("🗄️ Phase 4: Database Optimization Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check database optimizer
    const optimizerPath = "src/lib/database-optimizer.ts";
    if (!existsSync(join(this.rootDir, optimizerPath))) {
      issues.push("Database optimizer not found");
    } else {
      const optimizerContent = this.readFileContent(optimizerPath);
      
      const requiredClasses = ["BatchOperations", "OptimizedQueries", "DatabaseHealth"];
      for (const className of requiredClasses) {
        if (!optimizerContent.includes(className)) {
          issues.push(`Missing database optimizer class: ${className}`);
        }
      }
    }

    // Check query optimizer
    const queryOptimizerPath = "src/lib/query-optimizer.ts";
    if (!existsSync(join(this.rootDir, queryOptimizerPath))) {
      issues.push("Query optimizer not found");
    } else {
      const queryContent = this.readFileContent(queryOptimizerPath);
      
      const requiredUtilities = ["CursorPagination", "QueryBuilders", "DataLoaders"];
      for (const utility of requiredUtilities) {
        if (!queryContent.includes(utility)) {
          issues.push(`Missing query optimizer utility: ${utility}`);
        }
      }
    }

    // Check optimization script
    const scriptPath = "scripts/optimize-database-indexes.ts";
    if (!existsSync(join(this.rootDir, scriptPath))) {
      recommendations.push("Consider adding database index optimization script");
    }

    // Test database connection (if available)
    try {
      const prisma = createPrismaClient({ instanceId: "validation" });
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
    } catch (error) {
      recommendations.push("Database connection test failed - ensure DATABASE_URL is configured");
    }

    this.results.push({
      phase: "Phase 4: Database Optimization",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} Database Optimization: ${issues.length} issues found\n`);
  }

  private async validatePhase5PerformanceMonitoring(): Promise<void> {
    console.log("📊 Phase 5: Performance Monitoring Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check enhanced performance monitor
    const monitorPath = "src/lib/enhanced-performance-monitor.ts";
    if (!existsSync(join(this.rootDir, monitorPath))) {
      issues.push("Enhanced performance monitor not found");
    } else {
      const monitorContent = this.readFileContent(monitorPath);
      
      const requiredFeatures = [
        "WebVitalsMetric",
        "UserAnalyticsEvent", 
        "BusinessMetric",
        "PerformanceAlert",
        "EnhancedPerformanceMonitor"
      ];
      
      for (const feature of requiredFeatures) {
        if (!monitorContent.includes(feature)) {
          issues.push(`Missing performance monitoring feature: ${feature}`);
        }
      }
    }

    // Check user analytics
    const analyticsPath = "src/lib/user-analytics.ts";
    if (!existsSync(join(this.rootDir, analyticsPath))) {
      issues.push("User analytics service not found");
    } else {
      const analyticsContent = this.readFileContent(analyticsPath);
      
      if (!analyticsContent.includes("UserAnalyticsService")) {
        issues.push("User analytics service class not found");
      }
    }

    // Check performance hooks
    const hooksPath = "src/hooks/use-performance-monitoring.ts";
    if (!existsSync(join(this.rootDir, hooksPath))) {
      issues.push("Performance monitoring hooks not found");
    } else {
      const hooksContent = this.readFileContent(hooksPath);
      
      const requiredHooks = [
        "useComponentPerformance",
        "usePageTracking",
        "useFeatureTracking",
        "useApiPerformance"
      ];
      
      for (const hook of requiredHooks) {
        if (!hooksContent.includes(hook)) {
          issues.push(`Missing performance hook: ${hook}`);
        }
      }
    }

    // Check performance dashboard
    const dashboardPath = "src/components/admin/performance-dashboard.tsx";
    if (!existsSync(join(this.rootDir, dashboardPath))) {
      recommendations.push("Consider adding performance dashboard component");
    }

    this.results.push({
      phase: "Phase 5: Performance Monitoring",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} Performance Monitoring: ${issues.length} issues found\n`);
  }

  private async validatePhase6DocumentationTesting(): Promise<void> {
    console.log("📚 Phase 6: Documentation & Testing Validation");
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check documentation files
    const requiredDocs = [
      "docs/README.md",
      "docs/COMPONENT_MIGRATION.md",
      "docs/TRPC_MIGRATION.md", 
      "docs/DATABASE_OPTIMIZATION.md",
      "docs/PERFORMANCE_MONITORING.md",
      "docs/TESTING_GUIDE.md"
    ];

    for (const doc of requiredDocs) {
      if (!existsSync(join(this.rootDir, doc))) {
        issues.push(`Missing documentation: ${doc}`);
      }
    }

    // Check testing framework
    const testFrameworkPath = "src/test-utils/testing-framework.ts";
    if (!existsSync(join(this.rootDir, testFrameworkPath))) {
      issues.push("Testing framework not found");
    } else {
      const frameworkContent = this.readFileContent(testFrameworkPath);
      
      const requiredUtils = [
        "componentTestUtils",
        "apiTestUtils", 
        "dbTestUtils",
        "performanceTestUtils",
        "testDataFactory"
      ];
      
      for (const util of requiredUtils) {
        if (!frameworkContent.includes(util)) {
          issues.push(`Missing testing utility: ${util}`);
        }
      }
    }

    // Check test files
    const testFiles = this.getFilesInDirectory("src/__tests__", ".test.ts");
    if (testFiles.length === 0) {
      recommendations.push("Consider adding test files to validate functionality");
    }

    // Check Jest configuration
    const jestConfigPath = "jest.config.js";
    if (!existsSync(join(this.rootDir, jestConfigPath))) {
      recommendations.push("Consider adding Jest configuration for testing");
    }

    this.results.push({
      phase: "Phase 6: Documentation & Testing",
      passed: issues.length === 0,
      issues,
      recommendations,
    });

    console.log(`   ${issues.length === 0 ? "✅" : "❌"} Documentation & Testing: ${issues.length} issues found\n`);
  }

  private getFilesInDirectory(dir: string, extension?: string): string[] {
    const fullPath = join(this.rootDir, dir);
    if (!existsSync(fullPath)) return [];

    try {
      const files: string[] = [];
      const items = readdirSync(fullPath);
      
      for (const item of items) {
        const itemPath = join(fullPath, item);
        const stat = statSync(itemPath);
        
        if (stat.isFile()) {
          if (!extension || item.endsWith(extension)) {
            files.push(join(dir, item));
          }
        } else if (stat.isDirectory()) {
          files.push(...this.getFilesInDirectory(join(dir, item), extension));
        }
      }
      
      return files;
    } catch (error) {
      return [];
    }
  }

  private readFileContent(filePath: string): string {
    try {
      return readFileSync(join(this.rootDir, filePath), "utf-8");
    } catch (error) {
      return "";
    }
  }

  generateReport(): void {
    console.log("📋 Refactoring Validation Report");
    console.log("=".repeat(50));

    let totalIssues = 0;
    let totalRecommendations = 0;
    let passedPhases = 0;

    for (const result of this.results) {
      const status = result.passed ? "✅ PASSED" : "❌ FAILED";
      console.log(`\n${status} - ${result.phase}`);
      
      if (result.issues.length > 0) {
        console.log("  Issues:");
        result.issues.forEach(issue => console.log(`    • ${issue}`));
        totalIssues += result.issues.length;
      }
      
      if (result.recommendations.length > 0) {
        console.log("  Recommendations:");
        result.recommendations.forEach(rec => console.log(`    • ${rec}`));
        totalRecommendations += result.recommendations.length;
      }

      if (result.passed) passedPhases++;
    }

    console.log("\n" + "=".repeat(50));
    console.log("📊 Summary:");
    console.log(`   Phases Passed: ${passedPhases}/${this.results.length}`);
    console.log(`   Total Issues: ${totalIssues}`);
    console.log(`   Total Recommendations: ${totalRecommendations}`);
    
    if (totalIssues === 0) {
      console.log("\n🎉 All refactoring phases completed successfully!");
    } else {
      console.log(`\n⚠️  ${totalIssues} issues need to be addressed.`);
    }

    if (totalRecommendations > 0) {
      console.log(`💡 ${totalRecommendations} recommendations for further improvement.`);
    }
  }
}

async function main() {
  const validator = new RefactoringValidator();
  
  try {
    await validator.validateAll();
    validator.generateReport();
  } catch (error) {
    console.error("❌ Validation failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { RefactoringValidator };
