#!/usr/bin/env tsx
/**
 * Create Performance Baseline Script
 * 
 * Creates comprehensive performance baselines for refactoring validation.
 * Measures current performance across all key metrics.
 */

import { execSync } from "child_process";
import { writeFileSync, readFileSync, statSync } from "fs";
import { join } from "path";
import { performanceMonitor } from "../../src/lib/performance-monitor";

interface BaselineData {
  timestamp: string;
  version: string;
  environment: string;
  metrics: {
    codebase: {
      totalFiles: number;
      totalLines: number;
      largeFiles: Array<{ path: string; lines: number }>;
      averageFileSize: number;
    };
    performance: {
      buildTime: number;
      bundleSize: number;
      testRunTime: number;
      typeCheckTime: number;
    };
    quality: {
      complexity: number;
      duplication: number;
      testCoverage: number;
      lintIssues: number;
    };
    dependencies: {
      total: number;
      outdated: number;
      vulnerabilities: number;
      bundleAnalysis: Record<string, number>;
    };
  };
  targets: {
    codeReduction: number; // percentage
    performanceImprovement: number; // percentage
    qualityImprovement: number; // percentage
  };
}

class BaselineCreator {
  private baselinePath = join(process.cwd(), "refactor-baseline.json");
  
  /**
   * Create comprehensive baseline
   */
  async createBaseline(): Promise<BaselineData> {
    console.log("📊 Creating performance baseline...\n");
    
    const baseline: BaselineData = {
      timestamp: new Date().toISOString(),
      version: this.getVersion(),
      environment: process.env.NODE_ENV || "development",
      metrics: {
        codebase: await this.measureCodebase(),
        performance: await this.measurePerformance(),
        quality: await this.measureQuality(),
        dependencies: await this.measureDependencies(),
      },
      targets: {
        codeReduction: 30, // 30% code reduction target
        performanceImprovement: 25, // 25% performance improvement
        qualityImprovement: 20, // 20% quality improvement
      },
    };
    
    // Save baseline
    writeFileSync(this.baselinePath, JSON.stringify(baseline, null, 2));
    
    // Print summary
    this.printSummary(baseline);
    
    return baseline;
  }
  
  /**
   * Measure codebase metrics
   */
  private async measureCodebase(): Promise<BaselineData["metrics"]["codebase"]> {
    console.log("📁 Measuring codebase...");
    
    try {
      // Count files
      const fileCountOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | wc -l", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      const totalFiles = parseInt(fileCountOutput.trim()) || 0;
      
      // Count lines
      const lineCountOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | xargs wc -l | tail -1", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      // Use regex to reliably extract line count from wc output
      const lineMatch = lineCountOutput.match(/^\s*(\d+)\s+/);
      const totalLines = lineMatch ? parseInt(lineMatch[1]) : 0;
      
      // Find large files
      const largeFilesOutput = execSync("find src -name '*.ts' -o -name '*.tsx' | xargs wc -l | awk '$1 > 500 {print $1, $2}' | head -20", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const largeFiles = largeFilesOutput
        .split("\n")
        .filter(line => line.trim() && !line.includes("total"))
        .map(line => {
          const match = line.trim().match(/^(\d+)\s+(.+)$/);
          if (match) {
            const [, lines, path] = match;
            return { path: path.replace(process.cwd(), ""), lines: parseInt(lines) };
          }
          return null;
        })
        .filter(Boolean) as Array<{ path: string; lines: number }>;
      
      const averageFileSize = totalFiles > 0 ? totalLines / totalFiles : 0;
      
      console.log(`   Files: ${totalFiles}`);
      console.log(`   Lines: ${totalLines}`);
      console.log(`   Large files (>500 lines): ${largeFiles.length}`);
      console.log(`   Average file size: ${averageFileSize.toFixed(0)} lines`);
      
      return {
        totalFiles,
        totalLines,
        largeFiles,
        averageFileSize,
      };
    } catch (error) {
      console.log("   ⚠️ Could not measure codebase");
      return {
        totalFiles: 0,
        totalLines: 0,
        largeFiles: [],
        averageFileSize: 0,
      };
    }
  }
  
  /**
   * Measure performance metrics
   */
  private async measurePerformance(): Promise<BaselineData["metrics"]["performance"]> {
    console.log("⚡ Measuring performance...");
    
    let buildTime = 0;
    let bundleSize = 0;
    let testRunTime = 0;
    let typeCheckTime = 0;
    
    try {
      // Measure build time
      console.log("   Building application...");
      const buildStart = Date.now();
      execSync("pnpm build", { stdio: "ignore", cwd: process.cwd() });
      buildTime = Date.now() - buildStart;
      
      // Measure bundle size
      const buildDir = ".next";
      if (require("fs").existsSync(buildDir)) {
        const sizeOutput = execSync("du -sm .next", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        bundleSize = parseInt(sizeOutput.split("\t")[0]) || 0;
      } else {
        console.log("   ⚠️ Build directory not found, skipping bundle size measurement");
      }
      
      console.log(`   Build time: ${buildTime}ms`);
      console.log(`   Bundle size: ${bundleSize}MB`);
    } catch (error) {
      console.log("   ⚠️ Could not measure build performance");
    }
    
    try {
      // Measure test run time
      console.log("   Running tests...");
      const testStart = Date.now();
      execSync("pnpm test:unit:run", { stdio: "ignore", cwd: process.cwd() });
      testRunTime = Date.now() - testStart;
      
      console.log(`   Test run time: ${testRunTime}ms`);
    } catch (error) {
      console.log("   ⚠️ Could not measure test performance");
    }
    
    try {
      // Measure type check time
      console.log("   Type checking...");
      const typeStart = Date.now();
      execSync("pnpm check-types", { stdio: "ignore", cwd: process.cwd() });
      typeCheckTime = Date.now() - typeStart;
      
      console.log(`   Type check time: ${typeCheckTime}ms`);
    } catch (error) {
      console.log("   ⚠️ Could not measure type check performance");
    }
    
    return {
      buildTime,
      bundleSize,
      testRunTime,
      typeCheckTime,
    };
  }
  
  /**
   * Measure quality metrics
   */
  private async measureQuality(): Promise<BaselineData["metrics"]["quality"]> {
    console.log("🎯 Measuring quality...");
    
    let complexity = 0;
    let duplication = 0;
    let testCoverage = 0;
    let lintIssues = 0;
    
    try {
      // Measure test coverage by running command and reading coverage file
      execSync("pnpm test:coverage", { stdio: "ignore", cwd: process.cwd() });
      
      // Read coverage report from file
      const coverageDir = "coverage";
      const coverageFile = join(process.cwd(), coverageDir, "coverage-summary.json");
      if (require("fs").existsSync(coverageFile)) {
        try {
          const coverageData = JSON.parse(require("fs").readFileSync(coverageFile, "utf-8"));
          testCoverage = coverageData.total?.lines?.pct || 0;
        } catch (parseError) {
          console.log("   ⚠️ Could not parse coverage report");
        }
      }
      
      console.log(`   Test coverage: ${testCoverage}%`);
    } catch (error) {
      console.log("   ⚠️ Could not measure test coverage");
    }
    
    try {
      // Check lint issues with structured output
      const lintOutput = execSync("pnpm lint --format json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      // Parse lint output as JSON for accurate counting
      try {
        const lintResults = JSON.parse(lintOutput);
        lintIssues = lintResults.reduce((total: number, file: any) => {
          return total + (file.messages ? file.messages.length : 0);
        }, 0);
      } catch (parseError) {
        // Fallback to regex if JSON parsing fails
        const warningMatches = lintOutput.match(/\s+(warning|error)\s+/gi) || [];
        lintIssues = warningMatches.length;
      }
      
      console.log(`   Lint issues: ${lintIssues}`);
    } catch (error) {
      // Lint command might exit with non-zero code if issues found
      const errorOutput = error instanceof Error ? error.message : "";
      const warningMatches = errorOutput.match(/\s+(warning|error)\s+/gi) || [];
      lintIssues = warningMatches.length;
      
      console.log(`   Lint issues: ${lintIssues}`);
    }
    
    try {
      // Measure code duplication
      const jscpdOutput = execSync("npx jscpd src --format json --silent", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      const jscpdResult = JSON.parse(jscpdOutput);
      duplication = jscpdResult.statistics?.total?.percentage || 0;
      
      console.log(`   Code duplication: ${duplication}%`);
    } catch (error) {
      console.log("   ⚠️ Could not measure code duplication");
    }
    
    // Complexity measurement would require additional tools
    console.log(`   Complexity: ${complexity} (not measured)`);
    
    return {
      complexity,
      duplication,
      testCoverage,
      lintIssues,
    };
  }
  
  /**
   * Measure dependency metrics
   */
  private async measureDependencies(): Promise<BaselineData["metrics"]["dependencies"]> {
    console.log("📦 Measuring dependencies...");
    
    let total = 0;
    let outdated = 0;
    let vulnerabilities = 0;
    const bundleAnalysis: Record<string, number> = {};
    
    try {
      // Count total dependencies
      const packageJson = JSON.parse(readFileSync(join(process.cwd(), "package.json"), "utf-8"));
      const deps = Object.keys(packageJson.dependencies || {});
      const devDeps = Object.keys(packageJson.devDependencies || {});
      total = deps.length + devDeps.length;
      
      console.log(`   Total dependencies: ${total}`);
    } catch (error) {
      console.log("   ⚠️ Could not count dependencies");
    }
    
    try {
      // Check for outdated dependencies
      const outdatedOutput = execSync("pnpm outdated --json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      try {
        const outdatedResult = JSON.parse(outdatedOutput);
        outdated = Object.keys(outdatedResult || {}).length;
      } catch (parseError) {
        console.log("   ⚠️ Could not parse outdated dependencies output");
      }
      
      console.log(`   Outdated dependencies: ${outdated}`);
    } catch (error) {
      // pnpm outdated exits with non-zero code when outdated packages exist
      if (error instanceof Error && 'stdout' in error) {
        try {
          const outdatedResult = JSON.parse((error as any).stdout);
          outdated = Object.keys(outdatedResult || {}).length;
          console.log(`   Outdated dependencies: ${outdated}`);
        } catch (parseError) {
          console.log("   ⚠️ Could not parse outdated dependencies from error output");
        }
      } else {
        console.log("   ⚠️ Could not check outdated dependencies");
      }
    }
    
    try {
      // Check for vulnerabilities
      const auditOutput = execSync("pnpm audit --json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      const audit = JSON.parse(auditOutput);
      const vulns = audit.metadata?.vulnerabilities || {};
      vulnerabilities = Object.values(vulns).reduce((sum: number, count) => sum + (count as number), 0);
      
      console.log(`   Vulnerabilities: ${vulnerabilities}`);
    } catch (error) {
      console.log("   ⚠️ Could not check vulnerabilities");
    }
    
    return {
      total,
      outdated,
      vulnerabilities,
      bundleAnalysis,
    };
  }
  
  /**
   * Get current version
   */
  private getVersion(): string {
    try {
      const packageJson = JSON.parse(readFileSync(join(process.cwd(), "package.json"), "utf-8"));
      return packageJson.version || "unknown";
    } catch {
      return "unknown";
    }
  }
  
  /**
   * Print baseline summary
   */
  private printSummary(baseline: BaselineData): void {
    console.log("\n" + "=".repeat(60));
    console.log("📊 PERFORMANCE BASELINE CREATED");
    console.log("=".repeat(60));
    
    console.log(`\n📅 Created: ${baseline.timestamp}`);
    console.log(`🏷️ Version: ${baseline.version}`);
    console.log(`🌍 Environment: ${baseline.environment}`);
    
    console.log("\n📁 Codebase Metrics:");
    console.log(`   Files: ${baseline.metrics.codebase.totalFiles}`);
    console.log(`   Lines: ${baseline.metrics.codebase.totalLines}`);
    console.log(`   Large files: ${baseline.metrics.codebase.largeFiles.length}`);
    console.log(`   Avg file size: ${baseline.metrics.codebase.averageFileSize.toFixed(0)} lines`);
    
    console.log("\n⚡ Performance Metrics:");
    console.log(`   Build time: ${baseline.metrics.performance.buildTime}ms`);
    console.log(`   Bundle size: ${baseline.metrics.performance.bundleSize}MB`);
    console.log(`   Test time: ${baseline.metrics.performance.testRunTime}ms`);
    console.log(`   Type check: ${baseline.metrics.performance.typeCheckTime}ms`);
    
    console.log("\n🎯 Quality Metrics:");
    console.log(`   Test coverage: ${baseline.metrics.quality.testCoverage}%`);
    console.log(`   Code duplication: ${baseline.metrics.quality.duplication}%`);
    console.log(`   Lint issues: ${baseline.metrics.quality.lintIssues}`);
    
    console.log("\n📦 Dependency Metrics:");
    console.log(`   Total: ${baseline.metrics.dependencies.total}`);
    console.log(`   Outdated: ${baseline.metrics.dependencies.outdated}`);
    console.log(`   Vulnerabilities: ${baseline.metrics.dependencies.vulnerabilities}`);
    
    console.log("\n🎯 Refactoring Targets:");
    console.log(`   Code reduction: ${baseline.targets.codeReduction}%`);
    console.log(`   Performance improvement: ${baseline.targets.performanceImprovement}%`);
    console.log(`   Quality improvement: ${baseline.targets.qualityImprovement}%`);
    
    console.log(`\n💾 Baseline saved to: ${this.baselinePath}`);
    console.log("\n✅ Baseline creation complete!");
  }
}

// Run baseline creation if called directly
if (require.main === module) {
  const creator = new BaselineCreator();
  creator.createBaseline().catch(error => {
    console.error("❌ Baseline creation failed:", error);
    process.exit(1);
  });
}

export { BaselineCreator };
