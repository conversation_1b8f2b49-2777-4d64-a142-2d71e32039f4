#!/usr/bin/env tsx
/**
 * Code Quality Analysis Script
 * 
 * Comprehensive code quality analysis including complexity,
 * duplication, dead code detection, and maintainability metrics.
 */

import { execSync } from "child_process";
import { readFileSync, writeFileSync } from "fs";
import { join } from "path";

interface QualityMetrics {
  complexity: {
    average: number;
    max: number;
    files: Array<{ path: string; complexity: number }>;
  };
  duplication: {
    percentage: number;
    instances: number;
    files: string[];
  };
  maintainability: {
    score: number;
    grade: string;
    issues: string[];
  };
  deadCode: {
    files: string[];
    exports: string[];
  };
  dependencies: {
    circular: string[];
    unused: string[];
    outdated: string[];
  };
}

class QualityAnalyzer {
  private srcPath = join(process.cwd(), "src");
  private reportPath = join(process.cwd(), "quality-report.json");
  
  /**
   * Run comprehensive quality analysis
   */
  async analyze(): Promise<QualityMetrics> {
    console.log("🎯 Starting code quality analysis...\n");
    
    const metrics: QualityMetrics = {
      complexity: { average: 0, max: 0, files: [] },
      duplication: { percentage: 0, instances: 0, files: [] },
      maintainability: { score: 0, grade: "A", issues: [] },
      deadCode: { files: [], exports: [] },
      dependencies: { circular: [], unused: [], outdated: [] },
    };
    
    try {
      // Analyze complexity
      await this.analyzeComplexity(metrics);
      
      // Check for code duplication
      await this.analyzeDuplication(metrics);
      
      // Calculate maintainability
      await this.analyzeMaintainability(metrics);
      
      // Find dead code
      await this.analyzeDeadCode(metrics);
      
      // Check dependencies
      await this.analyzeDependencies(metrics);
      
      // Generate report
      this.generateReport(metrics);
      
    } catch (error) {
      console.error("❌ Quality analysis failed:", error);
      throw error;
    }
    
    return metrics;
  }
  
  /**
   * Analyze code complexity
   */
  private async analyzeComplexity(metrics: QualityMetrics): Promise<void> {
    console.log("🔍 Analyzing code complexity...");
    
    try {
      // Use TypeScript compiler API for complexity analysis
      const complexityData = await this.calculateComplexity();
      
      metrics.complexity = complexityData;
      
      const avgComplexity = complexityData.average;
      const maxComplexity = complexityData.max;
      
      console.log(`   Average complexity: ${avgComplexity.toFixed(2)}`);
      console.log(`   Maximum complexity: ${maxComplexity}`);
      
      if (maxComplexity > 15) {
        console.log("   ⚠️ High complexity files found");
      } else {
        console.log("   ✅ Complexity within acceptable range");
      }
      
    } catch (error) {
      console.log("   ⚠️ Could not analyze complexity");
      metrics.complexity = { average: 0, max: 0, files: [] };
    }
  }
  
  /**
   * Check for code duplication
   */
  private async analyzeDuplication(metrics: QualityMetrics): Promise<void> {
    console.log("🔍 Checking for code duplication...");
    
    try {
      const jscpdOutput = execSync("npx jscpd src --format json --silent", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      let jscpdResult;
      try {
        jscpdResult = JSON.parse(jscpdOutput);
      } catch (parseError) {
        console.log("   ⚠️ Could not parse jscpd output");
        return;
      }
      const statistics = jscpdResult.statistics?.total || {};
      
      metrics.duplication = {
        percentage: statistics.percentage || 0,
        instances: statistics.duplicates || 0,
        files: jscpdResult.duplicates?.map((d: any) => d.firstFile?.name).filter(Boolean) || [],
      };
      
      console.log(`   Duplication: ${metrics.duplication.percentage}%`);
      console.log(`   Instances: ${metrics.duplication.instances}`);
      
      if (metrics.duplication.percentage > 10) {
        console.log("   ⚠️ High code duplication detected");
      } else {
        console.log("   ✅ Code duplication within acceptable range");
      }
      
    } catch (error) {
      console.log("   ⚠️ Could not analyze duplication (jscpd not available)");
      metrics.duplication = { percentage: 0, instances: 0, files: [] };
    }
  }
  
  /**
   * Calculate maintainability score
   */
  private async analyzeMaintainability(metrics: QualityMetrics): Promise<void> {
    console.log("🔍 Calculating maintainability score...");
    
    try {
      // Simple maintainability calculation based on various factors
      let score = 100;
      const issues: string[] = [];
      
      // Penalize high complexity
      if (metrics.complexity.max > 15) {
        score -= 20;
        issues.push("High complexity functions detected");
      }
      
      // Penalize high duplication
      if (metrics.duplication.percentage > 10) {
        score -= 15;
        issues.push("High code duplication");
      }
      
      // Check file sizes
      const largeFiles = await this.findLargeFiles();
      if (largeFiles.length > 0) {
        score -= Math.min(20, largeFiles.length * 5);
        issues.push(`${largeFiles.length} large files (>500 lines)`);
      }
      
      // Check for TODO/FIXME comments
      const todoCount = await this.countTodoComments();
      if (todoCount > 10) {
        score -= Math.min(10, todoCount);
        issues.push(`${todoCount} TODO/FIXME comments`);
      }
      
      score = Math.max(0, score);
      
      let grade = "A";
      if (score < 90) grade = "B";
      if (score < 80) grade = "C";
      if (score < 70) grade = "D";
      if (score < 60) grade = "F";
      
      metrics.maintainability = { score, grade, issues };
      
      console.log(`   Maintainability score: ${score}/100 (Grade: ${grade})`);
      
      if (issues.length > 0) {
        console.log("   Issues:");
        issues.forEach(issue => console.log(`     • ${issue}`));
      }
      
    } catch (error) {
      console.log("   ⚠️ Could not calculate maintainability");
      metrics.maintainability = { score: 0, grade: "F", issues: ["Analysis failed"] };
    }
  }
  
  /**
   * Find dead code
   */
  private async analyzeDeadCode(metrics: QualityMetrics): Promise<void> {
    console.log("🔍 Finding dead code...");
    
    try {
      // Use unimported to find unused files
      const unimportedOutput = execSync("npx unimported --json", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      let unimportedResult;
      try {
        unimportedResult = JSON.parse(unimportedOutput);
      } catch (parseError) {
        console.log("   ⚠️ Could not parse unimported output");
        return;
      }
      
      metrics.deadCode = {
        files: unimportedResult.unimported || [],
        exports: unimportedResult.unresolved || [],
      };
      
      console.log(`   Unused files: ${metrics.deadCode.files.length}`);
      console.log(`   Unused exports: ${metrics.deadCode.exports.length}`);
      
      if (metrics.deadCode.files.length > 0 || metrics.deadCode.exports.length > 0) {
        console.log("   ⚠️ Dead code detected");
      } else {
        console.log("   ✅ No dead code found");
      }
      
    } catch (error) {
      console.log("   ⚠️ Could not analyze dead code (unimported not available)");
      metrics.deadCode = { files: [], exports: [] };
    }
  }
  
  /**
   * Analyze dependencies
   */
  private async analyzeDependencies(metrics: QualityMetrics): Promise<void> {
    console.log("🔍 Analyzing dependencies...");
    
    try {
      // Check for circular dependencies
      const madgeOutput = execSync("npx madge --circular --json src", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      let circularDeps;
      try {
        circularDeps = JSON.parse(madgeOutput);
      } catch (parseError) {
        console.log("   ⚠️ Could not parse madge output");
        circularDeps = [];
      }
      metrics.dependencies.circular = circularDeps || [];
      
      console.log(`   Circular dependencies: ${metrics.dependencies.circular.length}`);
      
      // Check for unused dependencies
      try {
        const depcheckOutput = execSync("npx depcheck --json", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        
        let depcheckResult;
        try {
          depcheckResult = JSON.parse(depcheckOutput);
        } catch (parseError) {
          console.log("   ⚠️ Could not parse depcheck output");
          return;
        }
        metrics.dependencies.unused = depcheckResult.dependencies || [];
        
        console.log(`   Unused dependencies: ${metrics.dependencies.unused.length}`);
      } catch {
        console.log("   ⚠️ Could not check unused dependencies");
      }
      
      // Check for outdated dependencies
      try {
        const outdatedOutput = execSync("pnpm outdated --json", {
          encoding: "utf-8",
          cwd: process.cwd(),
        });
        
        let outdatedResult;
        try {
          outdatedResult = JSON.parse(outdatedOutput);
        } catch (parseError) {
          console.log("   ⚠️ Could not parse outdated output");
          return;
        }
        metrics.dependencies.outdated = Object.keys(outdatedResult || {});
        
        console.log(`   Outdated dependencies: ${metrics.dependencies.outdated.length}`);
      } catch {
        console.log("   ⚠️ Could not check outdated dependencies");
      }
      
    } catch (error) {
      console.log("   ⚠️ Could not analyze dependencies");
      metrics.dependencies = { circular: [], unused: [], outdated: [] };
    }
  }
  
  /**
   * Calculate complexity for TypeScript files
   */
  private async calculateComplexity(): Promise<QualityMetrics["complexity"]> {
    // Simplified complexity calculation
    // In a real implementation, you'd use a proper complexity analyzer
    
    const files: Array<{ path: string; complexity: number }> = [];
    let totalComplexity = 0;
    let maxComplexity = 0;
    
    // This is a placeholder - implement actual complexity calculation
    // using tools like typescript-complexity or custom AST analysis
    
    return {
      average: files.length > 0 ? totalComplexity / files.length : 0,
      max: maxComplexity,
      files: files.sort((a, b) => b.complexity - a.complexity).slice(0, 10),
    };
  }
  
  /**
   * Find files larger than threshold
   */
  private async findLargeFiles(): Promise<string[]> {
    const largeFiles: string[] = [];
    
    try {
      const findOutput = execSync("find src -name '*.ts' -o -name '*.tsx' -print0 | xargs -0 wc -l", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      const lines = findOutput.split("\n");
      for (const line of lines) {
        const match = line.match(/^\s*(\d+)\s+(.+)$/);
        if (match) {
          const lineCount = parseInt(match[1]);
          const filePath = match[2];
          
          if (lineCount > 500 && !filePath.includes("total")) {
            largeFiles.push(filePath);
          }
        }
      }
    } catch {
      // Command failed or not available
    }
    
    return largeFiles;
  }
  
  /**
   * Count TODO/FIXME comments
   */
  private async countTodoComments(): Promise<number> {
    try {
      const grepOutput = execSync("grep -r 'TODO\\|FIXME\\|XXX' src --include='*.ts' --include='*.tsx' | wc -l", {
        encoding: "utf-8",
        cwd: process.cwd(),
      });
      
      return parseInt(grepOutput.trim()) || 0;
    } catch {
      return 0;
    }
  }
  
  /**
   * Generate quality report
   */
  private generateReport(metrics: QualityMetrics): void {
    console.log("\n📊 Generating quality report...");
    
    const report = {
      timestamp: new Date().toISOString(),
      metrics,
      summary: {
        overallScore: this.calculateOverallScore(metrics),
        recommendations: this.generateRecommendations(metrics),
      },
    };
    
    writeFileSync(this.reportPath, JSON.stringify(report, null, 2));
    
    console.log(`   Report saved to: ${this.reportPath}`);
    console.log(`   Overall quality score: ${report.summary.overallScore}/100`);
    
    if (report.summary.recommendations.length > 0) {
      console.log("\n💡 Recommendations:");
      report.summary.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }
  }
  
  /**
   * Calculate overall quality score
   */
  private calculateOverallScore(metrics: QualityMetrics): number {
    let score = metrics.maintainability.score;
    
    // Adjust based on other factors
    if (metrics.dependencies.circular.length > 0) {
      score -= 10;
    }
    
    if (metrics.deadCode.files.length > 5) {
      score -= 5;
    }
    
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Generate improvement recommendations
   */
  private generateRecommendations(metrics: QualityMetrics): string[] {
    const recommendations: string[] = [];
    
    if (metrics.complexity.max > 15) {
      recommendations.push("Refactor high-complexity functions");
    }
    
    if (metrics.duplication.percentage > 10) {
      recommendations.push("Extract common code to reduce duplication");
    }
    
    if (metrics.deadCode.files.length > 0) {
      recommendations.push("Remove unused files and exports");
    }
    
    if (metrics.dependencies.circular.length > 0) {
      recommendations.push("Resolve circular dependencies");
    }
    
    if (metrics.dependencies.unused.length > 0) {
      recommendations.push("Remove unused dependencies");
    }
    
    if (metrics.dependencies.outdated.length > 5) {
      recommendations.push("Update outdated dependencies");
    }
    
    return recommendations;
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new QualityAnalyzer();
  analyzer.analyze().catch(error => {
    console.error("❌ Quality analysis failed:", error);
    process.exit(1);
  });
}

export { QualityAnalyzer };
