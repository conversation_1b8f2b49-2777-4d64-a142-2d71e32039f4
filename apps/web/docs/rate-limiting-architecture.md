# Rate Limiting Architecture Documentation

## Overview

This document describes the consolidated rate limiting system implemented in BuddyChip's tRPC API. The system provides subscription-based rate limiting with comprehensive middleware patterns that eliminate code duplication and ensure consistent behavior across all API endpoints.

## Architecture Components

### 1. Core Middleware System (`trpc-middleware.ts`)

The core middleware system provides reusable components for:
- **Rate Limiting**: Subscription plan-based limits with automatic enforcement
- **Usage Tracking**: Real-time usage recording for billing and analytics
- **Security Validation**: Comprehensive security event logging
- **Performance Monitoring**: Response time tracking and metrics collection
- **Error Handling**: Consistent error responses with detailed context

### 2. Database Integration (`db-utils.ts`)

Rate limiting integrates with the database through:
- **Usage Logs**: Monthly billing period tracking
- **Plan Features**: Subscription plan limits and permissions
- **Rate Limit Checks**: Real-time limit validation
- **Security Events**: Audit trail for rate limit violations

### 3. Subscription Plan Integration

Rate limiting is tightly integrated with the subscription system:
- **Feature Types**: `AI_CALLS`, `COOKIE_API_CALLS`, `MONITORED_ACCOUNTS`, `IMAGE_GENERATIONS`
- **Plan Limits**: Free tier (limited), Pro tier (higher limits), Enterprise tier (unlimited/-1)
- **Billing Periods**: Monthly reset cycles with usage tracking

## Available Middleware Patterns

### 1. `createFeatureProcedure` - Standard Rate Limited Operations

**Use Case**: Standard operations that consume subscription resources

```typescript
createFeatureProcedure(FeatureType.AI_CALLS, {
  requestedAmount: 1,
  operationName: "generateResponse",
})
```

**Features**:
- Automatic rate limit checking
- Usage tracking after successful operations
- Performance monitoring
- Security event logging
- Comprehensive error handling

### 2. `createMonitoredProcedure` - Lightweight Operations

**Use Case**: Operations that don't consume subscription resources but need monitoring

```typescript
createMonitoredProcedure("getUserStats")
```

**Features**:
- Performance monitoring
- Security event logging
- No rate limiting or usage tracking
- Lightweight authentication

### 3. `createCacheAwareFeatureProcedure` - Cache-Optimized Operations

**Use Case**: Operations with caching where rate limiting only applies to cache misses

```typescript
createCacheAwareFeatureProcedure(FeatureType.COOKIE_API_CALLS, {
  requestedAmount: 1,
  operationName: "getCachedData",
})
```

**Features**:
- Conditional rate limiting (only on cache misses)
- Context-aware usage tracking
- Cache hit/miss logging

### 4. `createEnhancedFeatureProcedure` - Configurable Operations

**Use Case**: Operations needing custom configuration options

```typescript
createEnhancedFeatureProcedure(FeatureType.MONITORED_ACCOUNTS, {
  requestedAmount: 1,
  operationName: "addAccount",
  requireFeatureAccess: true,
  validateAccountOwnership: true,
  accountIdField: "accountId",
  enableCaching: false,
})
```

**Features**:
- Configurable middleware stack
- Optional account ownership validation
- Feature access control
- Caching support

### 5. `createConditionalRateLimitMiddleware` - Conditional Rate Limiting

**Use Case**: Operations where rate limiting depends on runtime conditions

```typescript
createConditionalRateLimitMiddleware(
  FeatureType.COOKIE_API_CALLS,
  1,
  (ctx) => ctx.cacheMiss === true // Only rate limit on cache misses
)
```

**Features**:
- Dynamic rate limiting based on conditions
- Context-aware enforcement
- Flexible application logic

### 6. `createBulkOperationMiddleware` - Bulk Operations

**Use Case**: Operations that process multiple items with variable resource consumption

```typescript
createBulkOperationMiddleware(
  FeatureType.AI_CALLS,
  (input) => input.items.length, // Calculate based on input size
  "processBulkItems"
)
```

**Features**:
- Dynamic resource calculation
- Batch size optimization
- Improved error messaging for bulk limits

## Rate Limiting Flow

### 1. Request Validation
```
1. Authentication check → 2. Feature access validation → 3. Rate limit check
```

### 2. Rate Limit Check Process
```typescript
// 1. Get user's subscription plan
const user = await prisma.user.findUnique({
  include: { plan: { include: { features: true } } }
});

// 2. Get current usage for billing period
const currentUsage = await prisma.usageLog.aggregate({
  where: { userId, feature, billingPeriod: getCurrentBillingPeriod() }
});

// 3. Check if request would exceed limits
const allowed = (currentUsage + requestedAmount) <= planLimit;
```

### 3. Usage Tracking
```typescript
// Record usage after successful operation
await prisma.usageLog.create({
  data: {
    userId,
    feature,
    amount,
    billingPeriod: getCurrentBillingPeriod(),
    metadata: operationContext
  }
});
```

## Error Handling

### Rate Limit Exceeded
```typescript
{
  code: "TOO_MANY_REQUESTS",
  message: "Rate limit exceeded for AI_CALLS. Used 95/100. Try again later."
}
```

### Feature Access Denied
```typescript
{
  code: "FORBIDDEN", 
  message: "Access denied for feature: IMAGE_GENERATIONS"
}
```

### Database Errors
```typescript
{
  code: "INTERNAL_SERVER_ERROR",
  message: "Database connection error. Please try again."
}
```

## Security Features

### 1. Rate Limit Violation Logging
```typescript
logSecurityEvent({
  type: "RATE_LIMIT_EXCEEDED",
  userId,
  details: {
    feature,
    requestedAmount,
    currentUsage,
    limit,
    path: "api.benji.generateResponse"
  }
});
```

### 2. Account Ownership Validation
```typescript
createAccountOwnershipMiddleware("accountId")
```

### 3. Input Validation
```typescript
createValidationMiddleware(userInputSchema, "userInput")
```

## Performance Monitoring

### 1. Response Time Tracking
```typescript
performanceMonitor.recordMetric({
  name: "generateResponse",
  duration: 1250, // ms
  path: "api.benji.generateResponse",
  userId,
  success: true,
  timestamp: Date.now()
});
```

### 2. Usage Analytics
```typescript
// Monthly usage summaries
const stats = await prisma.usageLog.groupBy({
  by: ["feature"],
  where: { userId, billingPeriod: currentPeriod },
  _sum: { amount: true }
});
```

## Migration Guide

### Migrating from Manual Rate Limiting

**Before**:
```typescript
// Manual rate limiting (old pattern)
const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
if (!rateLimit.allowed) {
  throw new TRPCError({
    code: "TOO_MANY_REQUESTS",
    message: "Rate limit exceeded"
  });
}

// ... operation logic ...

await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);
```

**After**:
```typescript
// Consolidated middleware (new pattern)
createFeatureProcedure(FeatureType.AI_CALLS, {
  requestedAmount: 1,
  operationName: "myOperation",
})
```

### Migrating Cached Operations

**Before**:
```typescript
// Manual conditional rate limiting
const cached = await getFromCache();
if (cached) {
  return cached;
}

const rateLimit = await checkRateLimit(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
if (!rateLimit.allowed) {
  throw new TRPCError({ code: "TOO_MANY_REQUESTS" });
}

const result = await fetchFromAPI();
await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
```

**After**:
```typescript
// Cache-aware middleware
createCacheAwareFeatureProcedure(FeatureType.COOKIE_API_CALLS, {
  requestedAmount: 1,
  operationName: "getCachedData",
})
```

## Best Practices

### 1. Choose the Right Middleware Pattern

- Use `createFeatureProcedure` for standard operations
- Use `createMonitoredProcedure` for non-consuming operations
- Use `createCacheAwareFeatureProcedure` for cached operations
- Use `createEnhancedFeatureProcedure` for complex requirements

### 2. Proper Error Handling

```typescript
try {
  // Operation logic
  return result;
} catch (error) {
  handleTRPCError(error, "operationName", {
    userId: ctx.userId,
    additionalContext: input
  });
}
```

### 3. Resource Calculation

```typescript
// For bulk operations
const calculateResourceUsage = (input) => {
  // Simple count
  return input.items.length;
  
  // Complex calculation
  return input.items.reduce((sum, item) => sum + item.complexity, 0);
};
```

### 4. Caching Strategy

```typescript
// Set cache miss context
const cached = await getFromCache();
if (cached) {
  return cached;
}

// Set context for middleware
ctx.cacheMiss = true;

// Fetch and cache
const result = await fetchFromAPI();
await setCache(result);
```

## Monitoring and Observability

### 1. Rate Limit Metrics
- Current usage per feature per user
- Rate limit violations per endpoint
- Success/failure rates by subscription plan

### 2. Performance Metrics
- Average response times per operation
- P95/P99 response time percentiles
- Database query performance

### 3. Security Metrics
- Rate limit violation patterns
- Unauthorized access attempts
- Account ownership validation failures

## Configuration

### Environment Variables
```env
# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_FAILED_REQUESTS=true

# Usage tracking
USAGE_TRACKING_ENABLED=true
USAGE_ANALYTICS_RETENTION_DAYS=90

# Security
SECURITY_LOGGING_ENABLED=true
RATE_LIMIT_VIOLATION_THRESHOLD=5
```

### Subscription Plan Configuration
```typescript
const planFeatures = {
  "reply-guy": {
    AI_CALLS: 100,
    COOKIE_API_CALLS: 50,
    MONITORED_ACCOUNTS: 3,
    IMAGE_GENERATIONS: 10
  },
  "pro-plan": {
    AI_CALLS: 1000,
    COOKIE_API_CALLS: 500,
    MONITORED_ACCOUNTS: 10,
    IMAGE_GENERATIONS: 100
  },
  "team-plan": {
    AI_CALLS: -1, // Unlimited
    COOKIE_API_CALLS: -1,
    MONITORED_ACCOUNTS: -1,
    IMAGE_GENERATIONS: -1
  }
};
```

## Troubleshooting

### Common Issues

1. **Rate Limit False Positives**
   - Check billing period calculation
   - Verify feature type mapping
   - Review usage log cleanup

2. **Cache Miss Issues**
   - Ensure cache context is properly set
   - Check cache invalidation logic
   - Verify conditional middleware conditions

3. **Performance Issues**
   - Monitor database query performance
   - Check rate limit check efficiency
   - Review usage tracking overhead

### Debug Logging

```typescript
// Enable detailed logging
process.env.VERBOSE_LOGGING = "true";
process.env.ENABLE_TRPC_REQUEST_LOGS = "true";
```

## Future Enhancements

### Planned Features
1. **Dynamic Rate Limiting**: Adjust limits based on system load
2. **User-Specific Limits**: Custom limits for enterprise customers
3. **Geographic Rate Limiting**: Location-based restrictions
4. **API Key Rate Limiting**: Separate limits for API access
5. **Advanced Analytics**: Machine learning-based usage prediction

### Performance Optimizations
1. **Redis Integration**: Faster rate limit checks
2. **Batch Usage Recording**: Reduce database load
3. **Async Usage Tracking**: Non-blocking usage recording
4. **Smart Caching**: Intelligent cache invalidation

This documentation provides a complete reference for the consolidated rate limiting system. For implementation examples, see the router files and middleware tests.