# Comprehensive Testing Guide

This guide outlines the testing strategy, frameworks, and best practices for the BuddyChip application, covering unit tests, integration tests, performance tests, and end-to-end testing.

## Testing Philosophy

Our testing approach follows the **Testing Pyramid** principle:
- **Unit Tests (70%)**: Fast, isolated tests for individual components and functions
- **Integration Tests (20%)**: Tests for component interactions and API endpoints
- **End-to-End Tests (10%)**: Full user journey tests across the entire application

## Testing Stack

### Core Testing Libraries
- **Jest**: Test runner and assertion library
- **React Testing Library**: Component testing utilities
- **Testing Library User Event**: User interaction simulation
- **MSW (Mock Service Worker)**: API mocking
- **Playwright**: End-to-end testing

### Custom Testing Framework
- **Testing Framework** (`test-utils/testing-framework.ts`): Unified testing utilities
- **Database Test Utils**: Database seeding and cleanup
- **Component Test Utils**: React component testing helpers
- **API Test Utils**: tRPC and API testing utilities
- **Performance Test Utils**: Performance monitoring and measurement

## Test Structure

### Directory Organization
```
src/
├── __tests__/
│   ├── components/
│   │   ├── ui/
│   │   ├── atoms/
│   │   └── pages/
│   ├── routers/
│   ├── lib/
│   ├── hooks/
│   └── utils/
├── test-utils/
│   ├── testing-framework.ts
│   ├── mocks/
│   └── fixtures/
└── e2e/
    ├── specs/
    ├── fixtures/
    └── utils/
```

### Test File Naming
- Unit tests: `*.test.ts` or `*.test.tsx`
- Integration tests: `*.integration.test.ts`
- E2E tests: `*.e2e.test.ts`
- Performance tests: `*.perf.test.ts`

## Writing Tests

### 1. Component Testing

#### Basic Component Test
```typescript
import { describe, it, expect } from "@jest/globals";
import { Button } from "@/components/ui/button";
import { componentTestUtils } from "@/test-utils/testing-framework";

describe("Button Component", () => {
  it("renders with correct text", () => {
    componentTestUtils.renderWithProviders(
      <Button>Click me</Button>
    );

    const button = componentTestUtils.screen.getByRole("button", { name: /click me/i });
    expect(button).toBeInTheDocument();
  });

  it("handles click events", async () => {
    const handleClick = jest.fn();
    componentTestUtils.renderWithProviders(
      <Button onClick={handleClick}>Click me</Button>
    );

    const button = componentTestUtils.screen.getByRole("button");
    await componentTestUtils.userInteraction.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

#### Testing with Hooks
```typescript
import { useFeatureTracking } from "@/hooks/use-performance-monitoring";
import { componentTestUtils } from "@/test-utils/testing-framework";

describe("useFeatureTracking", () => {
  it("tracks feature usage", () => {
    const { result } = componentTestUtils.renderHookWithProviders(() =>
      useFeatureTracking(FeatureType.AI_CALLS)
    );

    expect(result.current.isActive).toBe(false);

    act(() => {
      result.current.startFeature({ prompt: "test" });
    });

    expect(result.current.isActive).toBe(true);
  });
});
```

### 2. API Testing

#### tRPC Router Testing
```typescript
import { mentionsRouter } from "@/routers/mentions";
import { apiTestUtils, dbTestUtils } from "@/test-utils/testing-framework";

describe("Mentions Router", () => {
  beforeEach(async () => {
    await dbTestUtils.cleanup();
  });

  it("returns user mentions", async () => {
    const { user, mention } = await dbTestUtils.seed();
    const context = apiTestUtils.createMockContext({ userId: user.id });

    const result = await apiTestUtils.testProcedure(
      mentionsRouter.getLatest,
      { limit: 10 },
      context
    );

    expect(result.success).toBe(true);
    expect(result.data.mentions).toHaveLength(1);
  });
});
```

#### External API Mocking
```typescript
import { apiTestUtils } from "@/test-utils/testing-framework";

describe("Twitter Integration", () => {
  beforeEach(() => {
    apiTestUtils.mockExternalAPIs();
  });

  it("fetches tweets from Twitter API", async () => {
    const tweets = await twitterClient.getUserTweets("testuser");
    
    expect(tweets).toHaveLength(1);
    expect(tweets[0]).toMatchObject({
      content: expect.any(String),
      authorHandle: "testuser",
    });
  });
});
```

### 3. Database Testing

#### Database Operations
```typescript
import { dbTestUtils, testDataFactory } from "@/test-utils/testing-framework";

describe("Database Operations", () => {
  beforeEach(async () => {
    await dbTestUtils.cleanup();
  });

  it("creates and retrieves mentions", async () => {
    const user = await dbTestUtils.createTestUser();
    const account = await dbTestUtils.testPrisma.monitoredAccount.create({
      data: testDataFactory.account({ userId: user.id }),
    });

    const mention = await dbTestUtils.createTestMention(user.id, account.id);

    expect(mention).toMatchObject({
      userId: user.id,
      accountId: account.id,
      content: expect.any(String),
    });
  });
});
```

### 4. Performance Testing

#### Component Performance
```typescript
import { performanceTestUtils } from "@/test-utils/testing-framework";

describe("Component Performance", () => {
  it("renders quickly", async () => {
    const renderTime = await performanceTestUtils.measureRenderTime(() => {
      componentTestUtils.renderWithProviders(<ExpensiveComponent />);
    });

    expect(renderTime).toBeLessThan(100); // 100ms threshold
  });

  it("handles large datasets efficiently", async () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({ id: i }));
    
    const renderTime = await performanceTestUtils.measureRenderTime(() => {
      componentTestUtils.renderWithProviders(
        <DataTable data={largeDataset} />
      );
    });

    expect(renderTime).toBeLessThan(500); // 500ms for large datasets
  });
});
```

#### Memory Usage Testing
```typescript
describe("Memory Usage", () => {
  it("does not leak memory", () => {
    const initialMemory = performanceTestUtils.measureMemoryUsage();
    
    // Render and unmount component multiple times
    for (let i = 0; i < 100; i++) {
      const { unmount } = componentTestUtils.renderWithProviders(<Component />);
      unmount();
    }

    const finalMemory = performanceTestUtils.measureMemoryUsage();
    
    if (initialMemory && finalMemory) {
      const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB threshold
    }
  });
});
```

### 5. Integration Testing

#### Full Feature Flow
```typescript
describe("AI Response Generation Flow", () => {
  it("generates response for mention", async () => {
    // Setup
    const { user, mention } = await dbTestUtils.seed();
    
    // Mock AI service
    const mockAIResponse = "This is a test AI response";
    jest.spyOn(aiService, "generateResponse").mockResolvedValue(mockAIResponse);

    // Test component
    componentTestUtils.renderWithProviders(
      <MentionCard mention={mention} />
    );

    // Trigger AI generation
    const generateButton = componentTestUtils.screen.getByRole("button", { 
      name: /generate response/i 
    });
    await componentTestUtils.userInteraction.click(generateButton);

    // Verify loading state
    expect(componentTestUtils.screen.getByText(/generating/i)).toBeInTheDocument();

    // Wait for completion
    await componentTestUtils.waitFor(() => {
      expect(componentTestUtils.screen.getByText(mockAIResponse)).toBeInTheDocument();
    });

    // Verify database update
    const responses = await dbTestUtils.testPrisma.aIResponse.findMany({
      where: { mentionId: mention.id },
    });
    expect(responses).toHaveLength(1);
    expect(responses[0].content).toBe(mockAIResponse);
  });
});
```

## Test Configuration

### Jest Configuration (`jest.config.js`)
```javascript
module.exports = {
  testEnvironment: "jsdom",
  setupFilesAfterEnv: ["<rootDir>/src/test-utils/setup.ts"],
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/src/$1",
  },
  testMatch: [
    "**/__tests__/**/*.(test|spec).(ts|tsx)",
    "**/*.(test|spec).(ts|tsx)",
  ],
  collectCoverageFrom: [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/test-utils/**",
    "!src/__tests__/**",
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### Test Setup (`test-utils/setup.ts`)
```typescript
import "@testing-library/jest-dom";
import { testSetup } from "./testing-framework";

// Global test setup
beforeAll(async () => {
  await testSetup.beforeAll();
});

beforeEach(async () => {
  await testSetup.beforeEach();
});

afterAll(async () => {
  await testSetup.afterAll();
});

// Mock environment variables
process.env.NODE_ENV = "test";
process.env.TEST_DATABASE_URL = "postgresql://test:test@localhost:5432/buddychip_test";
```

## Running Tests

### Test Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:unit": "jest --testPathPattern=__tests__",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "playwright test",
    "test:perf": "jest --testPathPattern=perf",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

### Running Specific Tests
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm run test -- button.test.tsx

# Run tests with coverage
npm run test:coverage

# Run integration tests only
npm run test:integration

# Run E2E tests
npm run test:e2e
```

## Test Data Management

### Test Database Setup
```bash
# Create test database
createdb buddychip_test

# Run migrations
DATABASE_URL="postgresql://test:test@localhost:5432/buddychip_test" npx prisma migrate deploy

# Generate test client
DATABASE_URL="postgresql://test:test@localhost:5432/buddychip_test" npx prisma generate
```

### Data Factories
```typescript
// Use consistent test data
const user = testDataFactory.user({
  email: "<EMAIL>",
  name: "Test User",
});

const mention = testDataFactory.mention({
  content: "Test mention content",
  bullishScore: 75,
});
```

## Best Practices

### 1. Test Organization
- Group related tests with `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert
- Keep tests focused and test one thing at a time

### 2. Mocking Strategy
- Mock external dependencies (APIs, services)
- Use real database for integration tests
- Mock performance monitoring in tests
- Avoid mocking internal application logic

### 3. Assertions
- Use specific assertions (`toHaveLength(1)` vs `toBeTruthy()`)
- Test both positive and negative cases
- Verify side effects (database changes, API calls)
- Use `waitFor` for asynchronous operations

### 4. Test Maintenance
- Update tests when requirements change
- Remove obsolete tests
- Refactor test utilities when patterns emerge
- Keep test data factories up to date

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: npm

      - run: npm ci
      - run: npm run test:ci
      - run: npm run test:e2e

      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Debugging Tests

### Common Issues
1. **Async operations**: Use `waitFor` and proper async/await
2. **Database state**: Ensure proper cleanup between tests
3. **Mock conflicts**: Clear mocks between tests
4. **Environment variables**: Set test-specific values

### Debugging Tools
```bash
# Run tests with debug output
DEBUG=* npm run test

# Run single test with verbose output
npm run test -- --verbose button.test.tsx

# Debug test in VS Code
# Add breakpoints and use "Jest: Debug" configuration
```

## Coverage Requirements

### Coverage Thresholds
- **Statements**: 80%
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%

### Critical Areas (95%+ coverage required)
- Authentication and authorization
- Payment processing
- Data validation and sanitization
- Security-related functions
- Core business logic

### Excluded from Coverage
- Type definitions
- Test utilities
- Configuration files
- Generated code (Prisma client)

## Performance Testing

### Benchmarks
- Component render time: < 100ms
- API response time: < 500ms
- Database queries: < 100ms
- Page load time: < 2s

### Load Testing
```typescript
describe("Load Testing", () => {
  it("handles concurrent requests", async () => {
    const promises = Array.from({ length: 100 }, () =>
      apiTestUtils.testProcedure(router.getLatest, { limit: 10 }, context)
    );

    const results = await Promise.all(promises);
    
    expect(results.every(r => r.success)).toBe(true);
  });
});
```
