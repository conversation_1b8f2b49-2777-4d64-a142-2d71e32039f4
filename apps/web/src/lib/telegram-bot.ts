/**
 * Telegram Bot Service
 *
 * Core Telegram bot implementation that integrates with Benji AI agent
 */

import TelegramBot from "node-telegram-bot-api";
// import { checkRateLimit, recordUsage } from './db-utils'; // Using enhanced Telegram versions instead
import { FeatureType } from "../../prisma/generated";
import { getBenjiForUser } from "./benji";
import { prisma } from "./db-utils";
import {
  getTelegramBenjiForUser,
  TelegramBenjiAgent,
} from "./telegram-benji-agent";
import { telegramLogger } from "./telegram-logger";
import {
  checkTelegramFeatureLimit,
  checkTelegramSpam,
  checkTelegramUserRateLimit,
  recordTelegramFeatureUsage,
} from "./telegram-rate-limiting";
import {
  hasSecurityRisk,
  parseCommand,
  sanitizeCallbackData,
  sanitizeText,
  shouldRejectBasedOnContent,
  validateMessage,
  validateTwitterUrl,
} from "./telegram-validation";

export interface TelegramBotConfig {
  token: string;
  webhookUrl?: string;
  enablePolling?: boolean;
}

export class TelegramBotService {
  private bot: TelegramBot;
  private config: TelegramBotConfig;

  constructor(config: TelegramBotConfig) {
    this.config = config;
    this.bot = new TelegramBot(config.token, {
      polling: config.enablePolling || false,
      webHook: !config.enablePolling,
    });

    telegramLogger.info("TelegramBotService initialized", {
      metadata: {
        enablePolling: config.enablePolling,
        webhookUrl: config.webhookUrl,
      },
    });

    // Event handlers removed - using manual update processing instead
    // this.setupEventHandlers();
  }

  /**
   * Set up webhook for production use
   */
  async setWebhook(url: string) {
    try {
      await this.bot.setWebHook(url);
      telegramLogger.info("Webhook set successfully", {
        metadata: { webhookUrl: url },
      });
    } catch (error) {
      telegramLogger.error("Failed to set webhook", {
        error: error instanceof Error ? error : new Error(String(error)),
        metadata: { webhookUrl: url },
      });
      throw error;
    }
  }

  /**
   * Get current webhook info from Telegram
   */
  async getWebHookInfo() {
    try {
      return await this.bot.getWebHookInfo();
    } catch (error) {
      telegramLogger.error("Failed to get webhook info", {
        error: error instanceof Error ? error : new Error(String(error)),
      });
      throw error;
    }
  }

  /**
   * Process incoming webhook updates with comprehensive validation
   */
  async processUpdate(update: any) {
    try {
      console.log("📨 Telegram: Processing update:", {
        updateId: update.update_id,
        messageId: update.message?.message_id,
        chatId: update.message?.chat?.id,
        from: update.message?.from?.username,
      });

      // Validate message if present
      if (update.message) {
        const validation = validateMessage(update.message);
        if (!validation.isValid) {
          console.warn("⚠️ Telegram: Invalid message:", validation.errors);
          return; // Silently ignore invalid messages
        }

        // Use sanitized message
        update.message = validation.sanitizedMessage;

        // Security checks
        if (update.message.text) {
          const securityCheck = hasSecurityRisk(update.message.text);
          if (securityCheck.hasRisk) {
            console.warn(
              "🚨 Telegram: Security risk detected:",
              securityCheck.risks
            );
            await this.bot.sendMessage(
              update.message.chat.id,
              "⚠️ Your message contains potentially unsafe content and cannot be processed."
            );
            return;
          }

          const contentCheck = shouldRejectBasedOnContent(update.message.text);
          if (contentCheck.shouldReject) {
            console.warn("🚫 Telegram: Content rejected:", contentCheck.reason);
            await this.bot.sendMessage(
              update.message.chat.id,
              "⚠️ Your message appears to be spam or contains too many links/mentions."
            );
            return;
          }

          // Check Telegram-specific rate limits and spam
          const userId = update.message.from?.id.toString();
          if (userId) {
            const rateLimitCheck = await checkTelegramUserRateLimit(
              userId,
              "message"
            );
            if (!rateLimitCheck.allowed) {
              console.warn(
                "⚠️ Telegram: Rate limit exceeded:",
                rateLimitCheck.reason
              );
              if (rateLimitCheck.isSpam) {
                await this.bot.sendMessage(
                  update.message.chat.id,
                  "🚫 Anti-spam protection activated. Please wait before sending more messages."
                );
              } else {
                await this.bot.sendMessage(
                  update.message.chat.id,
                  `⚠️ Rate limit exceeded. Please wait ${Math.ceil((rateLimitCheck.resetTime! - Date.now()) / 1000)} seconds.`
                );
              }
              return;
            }

            const spamCheck = checkTelegramSpam(userId, update.message.text);
            if (spamCheck.isSpam) {
              console.warn("🚫 Telegram: Spam detected:", spamCheck.reason);
              await this.bot.sendMessage(
                update.message.chat.id,
                "🚫 Spam detected. Please avoid repeating messages or sending too many messages quickly."
              );
              return;
            }
          }
        }
      }

      // Validate callback query if present
      if (update.callback_query) {
        if (update.callback_query.data) {
          const sanitizedData = sanitizeCallbackData(
            update.callback_query.data
          );
          if (sanitizedData !== update.callback_query.data) {
            console.warn("⚠️ Telegram: Callback data sanitized");
            update.callback_query.data = sanitizedData;
          }
        }
      }

      // FIXED: Directly handle the update instead of calling bot.processUpdate again
      // This prevents double processing that was causing the bot to not respond
      await this.handleUpdateDirectly(update);
    } catch (error) {
      console.error("❌ Telegram: Error processing update:", error);
      throw error;
    }
  }

  /**
   * Handle update directly without going through bot.processUpdate again
   */
  private async handleUpdateDirectly(update: any) {
    try {
      // Handle messages
      if (update.message) {
        const msg = update.message;

        // Handle commands
        if (msg.text?.startsWith("/")) {
          const command = msg.text.split(" ")[0];
          switch (command) {
            case "/start":
              await this.handleStartCommand(msg);
              break;
            case "/help":
              await this.handleHelpCommand(msg);
              break;
            case "/settings":
              await this.handleSettingsCommand(msg);
              break;
            case "/status":
              await this.handleStatusCommand(msg);
              break;
            case "/create":
              await this.handleCreateCommand(msg);
              break;
            default:
              console.log(`⚡ Telegram: Unknown command: ${command}`);
              break;
          }
          return;
        }

        // Handle Twitter URLs
        if (
          msg.text &&
          /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/.test(msg.text)
        ) {
          console.log(
            `🐦 Telegram: Twitter URL detected in message ${msg.message_id}`
          );
          await this.handleTwitterUrl(msg);
          return;
        }

        // Handle general messages
        if (msg.text) {
          console.log(
            `💬 Telegram: General message detected in message ${msg.message_id}`
          );
          await this.handleGeneralMessage(msg);
          return;
        }
      }

      // Handle callback queries
      if (update.callback_query) {
        await this.handleCallbackQuery(update.callback_query);
        return;
      }

      console.log("📨 Telegram: Update processed (no specific handler needed)");
    } catch (error) {
      console.error("❌ Telegram: Error in handleUpdateDirectly:", error);
      throw error;
    }
  }

  /**
   * Set up event handlers for bot interactions
   */
  private setupEventHandlers() {
    // Handle /start command
    this.bot.onText(/\/start/, async (msg) => {
      await this.handleStartCommand(msg);
    });

    // Handle /help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelpCommand(msg);
    });

    // Handle /settings command
    this.bot.onText(/\/settings/, async (msg) => {
      await this.handleSettingsCommand(msg);
    });

    // Handle /status command
    this.bot.onText(/\/status/, async (msg) => {
      await this.handleStatusCommand(msg);
    });

    // Handle /create command
    this.bot.onText(/\/create/, async (msg) => {
      await this.handleCreateCommand(msg);
    });

    // Handle Twitter/X URLs
    this.bot.onText(
      /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/,
      async (msg) => {
        console.log(
          `🐦 Telegram: Twitter URL handler triggered for message ${msg.message_id}`
        );
        await this.handleTwitterUrl(msg);
      }
    );

    // Handle general messages
    this.bot.on("message", async (msg) => {
      // Skip if it's a command (already handled above)
      if (msg.text?.startsWith("/")) {
        console.log(
          `⚡ Telegram: Skipping command message ${msg.message_id} in general handler`
        );
        return;
      }

      // Skip if message contains Twitter URL (already handled above)
      if (
        msg.text &&
        /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/.test(msg.text)
      ) {
        console.log(
          `🐦 Telegram: Skipping Twitter URL message ${msg.message_id} in general handler`
        );
        return;
      }

      console.log(
        `💬 Telegram: General message handler triggered for message ${msg.message_id}`
      );
      await this.handleGeneralMessage(msg);
    });

    // Handle callback queries (inline keyboard responses)
    this.bot.on("callback_query", async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on("error", (error) => {
      console.error("❌ Telegram Bot Error:", error);
    });
  }

  /**
   * Handle /start command
   */
  private async handleStartCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    console.log("🚀 Telegram: /start command handler called for chat", chatId);
    console.log("🔍 Telegram: Bot instance details:", {
      token: this.config.token?.substring(0, 10) + "...",
      enablePolling: this.config.enablePolling,
      botExists: !!this.bot,
    });

    // Test the bot instance by getting bot info first
    try {
      console.log("🤖 Telegram: Testing bot instance with getMe...");
      const botInfo = await this.bot.getMe();
      console.log("✅ Telegram: Bot info retrieved:", {
        id: botInfo.id,
        username: botInfo.username,
        first_name: botInfo.first_name,
      });
    } catch (error) {
      console.error("❌ Telegram: Failed to get bot info:", error);
      return;
    }

    try {
      telegramLogger.logCommandReceived(
        "/start",
        telegramUser.id.toString(),
        chatId,
        telegramUser.username
      );

      // Get or create user in database
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      // Create personalized welcome message
      const firstName = telegramUser.first_name || "there";
      const isLinked = !!dbTelegramUser.userId;

      const welcomeMessage = `🤖 *Welcome to BuddyChip AI, ${firstName}!*

I'm Benji, your AI-powered assistant for Twitter engagement and intelligent conversations. Here's what I can do for you:

🐦 *Twitter/X Integration*
• Send me any Twitter/X link for instant AI-generated replies
• Perfect for engaging with your audience
• Contextual responses that match the conversation

🧠 *AI Conversations*
• Ask me anything - I'll provide intelligent responses
• Real-time web search for current information
• Context-aware multi-turn conversations

🎨 *Image Generation*
• Request AI-generated images with detailed prompts
• High-quality DALL-E 3 powered visuals
• Perfect for content creation

${
  isLinked
    ? `✅ *Account Status: Connected*
Your Telegram is linked to your BuddyChip account. You have full access to all features!

🚀 *Quick Start:*
• Send me a Twitter/X link to generate a reply
• Ask me any question for an AI response
• Use /help to see all available commands`
    : `⚠️ *Account Status: Limited Access*
Link your Telegram to your BuddyChip account to unlock all features and remove usage limits.

🔗 *Get Full Access:*
1. Visit buddychip.app to create an account
2. Use /settings to link your Telegram
3. Enjoy unlimited AI conversations!`
}

*Available Commands:*
/help - Show detailed help and examples
/settings - Manage account and preferences
/status - Check usage limits and statistics

Ready to get started? Try sending me a Twitter link or ask me anything! 🚀`;

      // Create inline keyboard based on account status
      const keyboard = isLinked
        ? [
            [
              {
                text: "🌐 Open Dashboard",
                url: "https://www.buddychip.app/dashboard",
              },
              { text: "📊 View Stats", callback_data: "view_stats" },
            ],
            [
              { text: "🐦 Try Twitter Link", callback_data: "twitter_example" },
              { text: "💬 Ask AI Question", callback_data: "ai_example" },
            ],
            [{ text: "📚 Help & Examples", callback_data: "show_help" }],
          ]
        : [
            [
              { text: "🔗 Link Account", callback_data: "link_account" },
              { text: "🌐 Create Account", url: "https://www.buddychip.app" },
            ],
            [
              { text: "🐦 Try Twitter Link", callback_data: "twitter_example" },
              { text: "💬 Ask AI Question", callback_data: "ai_example" },
            ],
            [{ text: "📚 Help & Examples", callback_data: "show_help" }],
          ];

      await this.bot.sendMessage(chatId, welcomeMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });

      telegramLogger.info("Welcome message sent successfully", {
        chatId,
        userId: telegramUser.id.toString(),
        metadata: { command: "/start", isLinked },
      });
    } catch (error) {
      telegramLogger.error("Error in /start command", {
        chatId,
        telegramUserId: telegramUser.id.toString(),
        error: error instanceof Error ? error : new Error(String(error)),
      });

      await this.bot.sendMessage(
        chatId,
        "❌ Sorry, something went wrong. Please try again or contact support."
      );
    }
  }

  /**
   * Handle /help command
   */
  private async handleHelpCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;

    const helpMessage = `
📚 *BuddyChip AI Help*

*Available Commands:*
/start - Welcome message and setup
/help - Show this help message
/settings - Account settings and preferences
/status - Check usage limits and account status

*Features:*
🐦 *Twitter Integration*
• Send any Twitter/X link
• Get instant AI-generated replies
• Perfect for social media engagement

🔍 *Web Search*
• Ask questions about current events
• Get real-time information
• Powered by advanced search tools

🎨 *Image Generation*
• Request AI-generated images
• Use detailed prompts for best results
• High-quality DALL-E 3 powered

💬 *Conversations*
• Natural language interactions
• Context-aware responses
• Multi-turn conversations

*Tips:*
• Be specific in your requests
• Use detailed prompts for images
• Check /status for usage limits
• Link your account for full access

Need more help? Contact support through the BuddyChip dashboard.
    `;

    await this.bot.sendMessage(chatId, helpMessage, {
      parse_mode: "Markdown",
    });
  }

  /**
   * Handle /settings command
   */
  private async handleSettingsCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    try {
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      const settingsMessage = `
⚙️ *Account Settings*

*Account Status:*
${
  dbTelegramUser.userId
    ? "✅ Linked to BuddyChip account"
    : "❌ Not linked - limited functionality"
}

*Current Settings:*
• Language: ${telegramUser.language_code || "en"}
• Active: ${dbTelegramUser.isActive ? "✅" : "❌"}

${
  !dbTelegramUser.userId
    ? "*To unlock all features:*\n1. Visit buddychip.app\n2. Create an account\n3. Use the link below to connect"
    : "*Manage your subscription and preferences at buddychip.app*"
}
      `;

      const keyboard = dbTelegramUser.userId
        ? [
            [
              {
                text: "🌐 Open Dashboard",
                url: "https://buddychip.app/dashboard",
              },
            ],
            [{ text: "🔄 Refresh Status", callback_data: "refresh_status" }],
          ]
        : [
            [{ text: "🔗 Link Account", callback_data: "link_account" }],
            [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
          ];

      await this.bot.sendMessage(chatId, settingsMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });
    } catch (error) {
      console.error("❌ Telegram: Error in /settings command:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error loading settings. Please try again."
      );
    }
  }

  /**
   * Handle /status command
   */
  private async handleStatusCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    try {
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountLinkingMessage(chatId, "status");
        return;
      }

      // Get user's plan and usage information
      const user = await prisma.user.findUnique({
        where: { id: dbTelegramUser.userId },
        include: {
          plan: {
            include: {
              features: true,
            },
          },
        },
      });

      if (!user) {
        await this.bot.sendMessage(chatId, "❌ User account not found.");
        return;
      }

      // Get feature limits from plan features
      const aiCallsFeature = user.plan.features.find(
        (f) => f.feature === "AI_CALLS"
      );
      const imageGenFeature = user.plan.features.find(
        (f) => f.feature === "IMAGE_GENERATIONS"
      );

      const aiCallsLimit = aiCallsFeature?.limit ?? 0;
      const imageGenLimit = imageGenFeature?.limit ?? 0;

      const statusMessage = `
📊 *Account Status*

*Plan:* ${user.plan.displayName}
*Status:* ${user.plan.isActive ? "✅ Active" : "❌ Inactive"}

*Usage Limits:*
• AI Calls: ${aiCallsLimit === -1 ? "Unlimited" : aiCallsLimit + "/month"}
• Image Generation: ${imageGenLimit === -1 ? "Unlimited" : imageGenLimit + "/month"}

*Account Info:*
• Member since: ${user.createdAt.toLocaleDateString()}
• Last active: ${user.lastActiveAt?.toLocaleDateString() || "Never"}

Visit buddychip.app for detailed usage analytics.
      `;

      await this.bot.sendMessage(chatId, statusMessage, {
        parse_mode: "Markdown",
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: "🌐 Open Dashboard",
                url: "https://buddychip.app/dashboard",
              },
            ],
          ],
        },
      });
    } catch (error) {
      console.error("❌ Telegram: Error in /status command:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error loading status. Please try again."
      );
    }
  }

  /**
   * Handle /create command
   */
  private async handleCreateCommand(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;

    if (!telegramUser) return;

    console.log("🎨 Telegram: /create command handler called for chat", chatId);

    try {
      // Extract the prompt from the command
      const commandText = msg.text || "";
      const prompt = commandText.replace("/create", "").trim();

      if (!prompt) {
        await this.bot.sendMessage(
          chatId,
          "🎨 *Create AI Post*\n\nPlease provide a prompt for your post:\n\n`/create Your prompt here`\n\nExample:\n`/create A motivational post about learning new skills`",
          { parse_mode: "Markdown" }
        );
        return;
      }

      // Get or create user in database
      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.bot.sendMessage(
          chatId,
          "🔗 *Account Required*\n\nTo use the /create command, you need to link your BuddyChip account first.\n\nUse /start to get started!",
          {
            parse_mode: "Markdown",
            reply_markup: {
              inline_keyboard: [
                [{ text: "🔗 Link Account", callback_data: "link_account" }],
                [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
              ],
            },
          }
        );
        return;
      }

      // Send "generating" message
      const generatingMsg = await this.bot.sendMessage(
        chatId,
        "🎨 *Generating your post...*\n\nThis may take a few moments.",
        { parse_mode: "Markdown" }
      );

      try {
        // Get Telegram Benji agent for the user
        const benji = await getTelegramBenjiForUser(
          dbTelegramUser.userId,
          telegramUser.id.toString(),
          chatId.toString()
        );

        // Generate the post
        const result = await benji.generateTelegramPost(prompt, {
          telegramUserId: telegramUser.id.toString(),
          telegramChatId: chatId.toString(),
        });

        // Process the streaming result to get the actual text
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        // Delete the "generating" message
        await this.bot.deleteMessage(chatId, generatingMsg.message_id);

        if (responseText.trim()) {
          // Send the generated post with action buttons
          await this.bot.sendMessage(
            chatId,
            `🎨 *Generated Post:*\n\n${responseText}`,
            {
              parse_mode: "Markdown",
              reply_markup: {
                inline_keyboard: [
                  [
                    {
                      text: "🔄 Regenerate",
                      callback_data: `regenerate:${prompt}`,
                    },
                    { text: "✨ Enhance", callback_data: `enhance:${prompt}` },
                  ],
                  [{ text: "📋 Copy", callback_data: `copy:${responseText}` }],
                ],
              },
            }
          );

          telegramLogger.info("Post generated successfully", {
            chatId,
            userId: telegramUser.id.toString(),
            metadata: { command: "/create", prompt: prompt.substring(0, 50) },
          });
        } else {
          await this.bot.sendMessage(
            chatId,
            "❌ Sorry, I couldn't generate a post right now. Please try again later.",
            { parse_mode: "Markdown" }
          );
        }
      } catch (error) {
        console.error("❌ Telegram: Error generating post:", error);

        // Delete the "generating" message
        try {
          await this.bot.deleteMessage(chatId, generatingMsg.message_id);
        } catch (deleteError) {
          console.error("Error deleting generating message:", deleteError);
        }

        await this.bot.sendMessage(
          chatId,
          "❌ Sorry, there was an error generating your post. Please try again later.",
          { parse_mode: "Markdown" }
        );
      }
    } catch (error) {
      telegramLogger.error("Error in /create command", {
        chatId,
        telegramUserId: telegramUser.id.toString(),
        error: error instanceof Error ? error : new Error(String(error)),
      });

      await this.bot.sendMessage(
        chatId,
        "❌ Sorry, something went wrong. Please try again or contact support."
      );
    }
  }

  /**
   * Get or create Telegram user record
   */
  private async getOrCreateTelegramUser(telegramUser: TelegramBot.User) {
    const telegramId = telegramUser.id.toString();

    let dbTelegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId },
    });

    if (!dbTelegramUser) {
      dbTelegramUser = await prisma.telegramUser.create({
        data: {
          telegramId,
          username: telegramUser.username,
          firstName: telegramUser.first_name,
          lastName: telegramUser.last_name,
          languageCode: telegramUser.language_code,
          lastActiveAt: new Date(),
        },
      });
      console.log("✅ Telegram: Created new user record:", telegramId);
    } else {
      // Update last active time and user info
      dbTelegramUser = await prisma.telegramUser.update({
        where: { id: dbTelegramUser.id },
        data: {
          username: telegramUser.username,
          firstName: telegramUser.first_name,
          lastName: telegramUser.last_name,
          languageCode: telegramUser.language_code,
          lastActiveAt: new Date(),
        },
      });
    }

    return dbTelegramUser;
  }

  /**
   * Send account linking message (centralized to avoid duplicates)
   */
  private async sendAccountLinkingMessage(
    chatId: number,
    context: string = "general"
  ) {
    const messages = {
      general:
        "🔗 Please link your BuddyChip account using /settings to unlock AI conversation features.\n\nYou can still use basic commands like /help and /start.",
      twitter:
        "🔗 Please link your BuddyChip account using /settings to use Twitter analysis features.",
      status:
        "🔗 Please link your BuddyChip account using /settings to view your account status and usage.",
    };

    const message =
      messages[context as keyof typeof messages] || messages.general;

    console.log(
      `📱 Telegram: Sending account linking message (${context}) to chat ${chatId}`
    );
    await this.bot.sendMessage(chatId, message, { parse_mode: "Markdown" });
  }

  /**
   * Handle Twitter/X URLs
   */
  private async handleTwitterUrl(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;
    const twitterUrl = msg.text;

    if (!telegramUser || !twitterUrl) return;

    try {
      console.log("🐦 Telegram: Processing Twitter URL:", twitterUrl);

      // Validate Twitter URL
      const urlValidation = validateTwitterUrl(twitterUrl);
      if (!urlValidation.isValid) {
        console.error("❌ Telegram: Invalid Twitter URL:", urlValidation.error);
        await this.bot.sendMessage(
          chatId,
          `❌ Invalid Twitter URL: ${urlValidation.error}`
        );
        return;
      }

      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountLinkingMessage(chatId, "twitter");
        return;
      }

      // Check Telegram rate limits first
      const telegramRateLimit = await checkTelegramUserRateLimit(
        telegramUser.id.toString(),
        "twitter_url"
      );
      if (!telegramRateLimit.allowed) {
        await this.bot.sendMessage(
          chatId,
          `⚠️ Too many Twitter URL requests. Please wait ${Math.ceil((telegramRateLimit.resetTime! - Date.now()) / 1000)} seconds.`
        );
        return;
      }

      // Check subscription feature limits
      const featureLimit = await checkTelegramFeatureLimit(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1
      );
      if (!featureLimit.allowed) {
        await this.bot.sendMessage(
          chatId,
          `⚠️ AI calls limit exceeded. ${featureLimit.remaining} remaining this month.`
        );
        return;
      }

      // Send "typing" indicator
      await this.bot.sendChatAction(chatId, "typing");

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Extract tweet content (simplified - in production you'd use Twitter API)
      const tweetContent = await this.extractTweetContent(twitterUrl);

      if (!tweetContent) {
        await this.bot.sendMessage(
          chatId,
          "❌ Could not extract tweet content. Please check the URL."
        );
        return;
      }

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramQuickReply(
        tweetContent,
        twitterUrl
      );

      // Convert streaming result to text
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      // Record usage
      await recordTelegramFeatureUsage(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1,
        {
          tweetUrl: twitterUrl,
          messageType: "telegram-quick-reply",
          telegramUserId: dbTelegramUser.telegramId,
          chatId: chatId.toString(),
        }
      );

      // Format response for Telegram
      const formattedResponse = `
🐦 *AI Reply Generated*

*Original Tweet:*
"${tweetContent.substring(0, 200)}${tweetContent.length > 200 ? "..." : ""}"

*Suggested Reply:*
${responseText}

*Actions:*
      `;

      // Store data in session for callback buttons (Telegram callback_data has 64 byte limit)
      const sessionData = {
        twitterUrl,
        responseText,
        timestamp: Date.now(),
      };

      // Store in user session for callback handling
      await this.storeSessionData(dbTelegramUser.id, sessionData);

      await this.bot.sendMessage(chatId, formattedResponse, {
        parse_mode: "Markdown",
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: "🔄 Regenerate",
                callback_data: `regenerate:${dbTelegramUser.id}`,
              },
              {
                text: "✨ Enhance",
                callback_data: `enhance:${dbTelegramUser.id}`,
              },
            ],
            [
              {
                text: "🚀 Send Reply",
                callback_data: `copy:${dbTelegramUser.id}`,
              },
            ],
          ],
        },
      });
    } catch (error) {
      console.error("❌ Telegram: Error processing Twitter URL:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error processing Twitter link. Please try again."
      );
    }
  }

  /**
   * Handle general messages (AI conversation)
   */
  private async handleGeneralMessage(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const telegramUser = msg.from;
    const messageText = msg.text;

    if (!telegramUser || !messageText) return;

    try {
      console.log(
        "💬 Telegram: Processing general message from:",
        telegramUser.username
      );

      // Sanitize message text
      const sanitizedText = sanitizeText(messageText);
      if (!sanitizedText) {
        console.warn("⚠️ Telegram: Empty message after sanitization");
        await this.bot.sendMessage(
          chatId,
          "⚠️ Your message cannot be processed. Please send a valid text message."
        );
        return;
      }

      const dbTelegramUser = await this.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountLinkingMessage(chatId, "general");
        return;
      }

      // Check subscription feature limits
      const featureLimit = await checkTelegramFeatureLimit(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1
      );
      if (!featureLimit.allowed) {
        await this.bot.sendMessage(
          chatId,
          `⚠️ AI calls limit exceeded. ${featureLimit.remaining} remaining this month.`
        );
        return;
      }

      // Send "typing" indicator
      await this.bot.sendChatAction(chatId, "typing");

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramResponse(sanitizedText, {
        telegramUserId: dbTelegramUser.id,
        telegramChatId: chatId.toString(),
      });

      // Convert streaming result to text
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      // Record usage
      await recordTelegramFeatureUsage(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1,
        {
          messageType: "telegram-conversation",
          telegramUserId: dbTelegramUser.telegramId,
          chatId: chatId.toString(),
          messageLength: sanitizedText.length,
        }
      );

      // Split long messages for Telegram's 4096 character limit
      const messages = benji.formatForTelegram(responseText);

      for (const message of messages) {
        await this.bot.sendMessage(chatId, message, {
          parse_mode: "Markdown",
          disable_web_page_preview: true,
        });
      }
    } catch (error) {
      console.error("❌ Telegram: Error processing general message:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Sorry, I encountered an error. Please try again."
      );
    }
  }

  /**
   * Handle callback queries from inline keyboards
   */
  private async handleCallbackQuery(query: TelegramBot.CallbackQuery) {
    const chatId = query.message?.chat.id;
    const data = query.data;

    if (!chatId || !data) return;

    try {
      console.log("🔘 Telegram: Processing callback query:", data);

      // Answer the callback query to remove loading state
      await this.bot.answerCallbackQuery(query.id);

      if (data === "link_account") {
        await this.handleLinkAccount(chatId);
      } else if (data === "help") {
        await this.handleHelpCommand({
          chat: { id: chatId },
        } as TelegramBot.Message);
      } else if (data === "refresh_status") {
        await this.handleStatusCommand({
          chat: { id: chatId },
          from: query.from,
        } as TelegramBot.Message);
      } else if (data.startsWith("regenerate:")) {
        await this.handleRegenerateReply(chatId, data);
      } else if (data.startsWith("enhance:")) {
        await this.handleEnhanceReply(chatId, data);
      } else if (data.startsWith("copy:")) {
        await this.handleCopyReply(chatId, data);
      }
    } catch (error) {
      console.error("❌ Telegram: Error processing callback query:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error processing request. Please try again."
      );
    }
  }

  /**
   * Handle account linking
   */
  private async handleLinkAccount(chatId: number) {
    try {
      // Import the generateTelegramLinkCode function
      const { generateTelegramLinkCode } = await import("./telegram-auth");

      // Generate a proper link code with all required parts
      const linkCodeData = generateTelegramLinkCode(chatId.toString());

      const linkMessage = `
🔗 *Link Your BuddyChip Account*

To unlock all AI features, you need to link your Telegram account with BuddyChip:

*Steps:*
1. Visit buddychip.app and create an account (if you don't have one)
2. Go to Profile → Telegram Integration
3. Click "Link Telegram Account"
4. Use the code below:

*Your Link Code:* \`${linkCodeData.code}\`

This code expires in 10 minutes for security.

*Why link your account?*
• Access to all AI tools and features
• Usage tracking and limits
• Personalized AI responses
• Premium model access (based on your plan)
      `;

      await this.bot.sendMessage(chatId, linkMessage, {
        parse_mode: "Markdown",
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: "🌐 Open BuddyChip",
                url: "https://buddychip.app/profile",
              },
            ],
          ],
        },
      });
    } catch (error) {
      console.error("❌ Telegram: Error generating link code:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error generating link code. Please try again."
      );
    }
  }

  /**
   * Extract tweet content from URL using existing Twitter API client
   */
  private async extractTweetContent(url: string): Promise<string | null> {
    try {
      console.log("🔍 Telegram: Extracting tweet content from:", url);

      // Import and use the existing Twitter API client
      const { twitterClient } = await import("./twitter-client");

      // Validate the URL first
      if (!twitterClient.validateTwitterUrl(url)) {
        console.error("❌ Telegram: Invalid Twitter URL format");
        return null;
      }

      // Extract tweet content using the existing API client
      const tweet = await twitterClient.getTweetFromUrl(url);

      if (!tweet) {
        console.error("❌ Telegram: Could not fetch tweet content");
        return null;
      }

      console.log("✅ Telegram: Successfully extracted tweet content:", {
        tweetId: tweet.id,
        author: tweet.author.userName,
        textLength: tweet.text.length,
      });

      return tweet.text;
    } catch (error) {
      console.error("❌ Telegram: Error extracting tweet content:", error);
      return null;
    }
  }

  /**
   * Split long messages to fit Telegram's character limit
   */
  private splitLongMessage(text: string, maxLength: number = 4000): string[] {
    if (text.length <= maxLength) {
      return [text];
    }

    const messages: string[] = [];
    let currentMessage = "";

    const lines = text.split("\n");

    for (const line of lines) {
      if ((currentMessage + line + "\n").length > maxLength) {
        if (currentMessage) {
          messages.push(currentMessage.trim());
          currentMessage = "";
        }

        // If a single line is too long, split it by words
        if (line.length > maxLength) {
          const words = line.split(" ");
          for (const word of words) {
            if ((currentMessage + word + " ").length > maxLength) {
              if (currentMessage) {
                messages.push(currentMessage.trim());
                currentMessage = "";
              }
            }
            currentMessage += word + " ";
          }
        } else {
          currentMessage = line + "\n";
        }
      } else {
        currentMessage += line + "\n";
      }
    }

    if (currentMessage.trim()) {
      messages.push(currentMessage.trim());
    }

    return messages;
  }

  /**
   * Store session data for callback handling
   */
  private async storeSessionData(telegramUserId: string, data: any) {
    try {
      // Find existing session
      const existingSession = await prisma.telegramSession.findFirst({
        where: {
          telegramUserId: telegramUserId,
          isActive: true,
        },
      });

      if (existingSession) {
        // Update existing session
        await prisma.telegramSession.update({
          where: { id: existingSession.id },
          data: {
            context: data,
            updatedAt: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Extend expiry
          },
        });
      } else {
        // Create new session
        await prisma.telegramSession.create({
          data: {
            telegramUserId: telegramUserId,
            context: data,
            isActive: true,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          },
        });
      }
    } catch (error) {
      console.error("❌ Telegram: Error storing session data:", error);
    }
  }

  /**
   * Get session data for callback handling
   */
  private async getSessionData(telegramUserId: string): Promise<any> {
    try {
      const session = await prisma.telegramSession.findFirst({
        where: {
          telegramUserId: telegramUserId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
      });
      return session?.context || null;
    } catch (error) {
      console.error("❌ Telegram: Error getting session data:", error);
      return null;
    }
  }

  /**
   * Handle regenerate reply callback
   */
  private async handleRegenerateReply(chatId: number, data: string) {
    try {
      const payload = data.split(":")[1];

      // Check if this is a /create command regeneration (payload is the prompt)
      if (payload && !payload.match(/^\d+$/)) {
        // This is a prompt from /create command
        await this.bot.sendMessage(chatId, "🔄 Regenerating post...");

        // Simulate the /create command with the same prompt
        await this.handleCreateCommand({
          chat: { id: chatId },
          text: `/create ${payload}`,
          from: { id: chatId }, // Simplified - in real implementation you'd get the actual user
        } as any);
        return;
      }

      // Original Twitter URL regeneration logic
      const telegramUserId = payload;
      const sessionData = await this.getSessionData(telegramUserId);

      if (!sessionData?.twitterUrl) {
        await this.bot.sendMessage(
          chatId,
          "❌ Session expired. Please send the Twitter URL again."
        );
        return;
      }

      await this.bot.sendMessage(chatId, "🔄 Regenerating reply...");

      // Simulate the Twitter URL message to reprocess
      await this.handleTwitterUrl({
        chat: { id: chatId },
        text: sessionData.twitterUrl,
        from: { id: chatId }, // Simplified - in real implementation you'd get the actual user
      } as any);
    } catch (error) {
      console.error("❌ Telegram: Error regenerating reply:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error regenerating reply. Please try again."
      );
    }
  }

  /**
   * Handle enhance reply callback
   */
  private async handleEnhanceReply(chatId: number, data: string) {
    try {
      const payload = data.split(":")[1];

      // Check if this is a /create command enhancement (payload is the prompt)
      if (payload && !payload.match(/^\d+$/)) {
        // This is a prompt from /create command
        await this.bot.sendMessage(
          chatId,
          "✨ Generating enhanced post with o3 model..."
        );

        // Get user info for enhanced generation
        const telegramUser = { id: chatId }; // Simplified - in real implementation you'd get the actual user
        const dbTelegramUser = await this.getOrCreateTelegramUser(
          telegramUser as any
        );

        if (!dbTelegramUser.userId) {
          await this.bot.sendMessage(
            chatId,
            "🔗 Account required for enhanced generation. Please link your account first."
          );
          return;
        }

        try {
          // Get Telegram Benji agent for enhanced generation
          const benji = await getTelegramBenjiForUser(
            dbTelegramUser.userId,
            telegramUser.id.toString(),
            chatId.toString()
          );

          // Generate enhanced post using o3 model
          const result = await benji.generateTelegramEnhancedResponse(payload, {
            telegramUserId: telegramUser.id.toString(),
            telegramChatId: chatId.toString(),
          });

          // Process the streaming result
          let responseText = "";
          for await (const chunk of result.textStream) {
            responseText += chunk;
          }

          if (responseText.trim()) {
            await this.bot.sendMessage(
              chatId,
              `✨ *Enhanced Post (o3 Model):*\n\n${responseText}`,
              { parse_mode: "Markdown" }
            );
          } else {
            await this.bot.sendMessage(
              chatId,
              "❌ Sorry, I couldn't generate an enhanced post right now. Please try again later."
            );
          }
        } catch (enhanceError) {
          console.error(
            "❌ Telegram: Error generating enhanced post:",
            enhanceError
          );
          await this.bot.sendMessage(
            chatId,
            "❌ Error generating enhanced post. Please try again."
          );
        }
        return;
      }

      // Original Twitter URL enhancement logic
      const telegramUserId = payload;
      const sessionData = await this.getSessionData(telegramUserId);

      if (!sessionData?.twitterUrl) {
        await this.bot.sendMessage(
          chatId,
          "❌ Session expired. Please send the Twitter URL again."
        );
        return;
      }

      await this.bot.sendMessage(
        chatId,
        "✨ Generating enhanced reply with o3 model..."
      );

      // This would integrate with the enhanced response functionality
      // For now, show a placeholder message
      await this.bot.sendMessage(
        chatId,
        "✨ Enhanced replies coming soon! This will use the OpenAI o3 model for highest quality responses.",
        { parse_mode: "Markdown" }
      );
    } catch (error) {
      console.error("❌ Telegram: Error enhancing reply:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error enhancing reply. Please try again."
      );
    }
  }

  /**
   * Handle copy reply callback
   */
  private async handleCopyReply(chatId: number, data: string) {
    try {
      const payload = data.split(":")[1];

      // Check if this is a /create command copy (payload is the generated content)
      if (payload && !payload.match(/^\d+$/)) {
        // This is generated content from /create command
        const encodedContent = encodeURIComponent(payload);
        const tweetUrl = `https://twitter.com/intent/tweet?text=${encodedContent}`;

        await this.bot.sendMessage(
          chatId,
          `📋 *Copy & Share Your Post:*\n\n\`\`\`\n${payload}\n\`\`\`\n\n🚀 [Share on Twitter](${tweetUrl})\n\n_Click the link above to open Twitter with your AI-generated post ready to share!_`,
          {
            parse_mode: "Markdown",
            disable_web_page_preview: true,
          }
        );
        return;
      }

      // Original Twitter URL copy logic
      const telegramUserId = payload;
      const sessionData = await this.getSessionData(telegramUserId);

      if (!sessionData?.responseText || !sessionData?.twitterUrl) {
        await this.bot.sendMessage(
          chatId,
          "❌ Session expired. Please send the Twitter URL again."
        );
        return;
      }

      // Extract tweet ID from the Twitter URL
      // Twitter URLs are like: https://twitter.com/username/status/1234567890 or https://x.com/username/status/1234567890
      const tweetIdMatch = sessionData.twitterUrl.match(/\/status\/(\d+)/);
      if (!tweetIdMatch?.[1]) {
        await this.bot.sendMessage(
          chatId,
          "❌ Could not extract tweet ID from URL. Please try with a direct tweet link."
        );
        return;
      }

      const tweetId = tweetIdMatch[1];

      // Create Twitter intent URL with pre-filled response
      const encodedResponse = encodeURIComponent(sessionData.responseText);
      const replyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodedResponse}`;

      // Send clickable link to open Twitter with pre-filled response
      await this.bot.sendMessage(
        chatId,
        `🚀 *Send your reply on Twitter:*\n\n[Open Twitter with pre-filled response](${replyUrl})\n\n_Click the link above to open Twitter with your AI-generated reply ready to send!_`,
        {
          parse_mode: "Markdown",
          disable_web_page_preview: true,
        }
      );
    } catch (error) {
      console.error("❌ Telegram: Error creating reply link:", error);
      await this.bot.sendMessage(
        chatId,
        "❌ Error copying reply. Please try again."
      );
    }
  }
}
