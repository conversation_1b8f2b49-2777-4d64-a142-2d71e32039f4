# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
pnpm install              # Install dependencies
pnpm dev                  # Start Next.js application in development
pnpm build               # Build application for production
pnpm check-types         # TypeScript type checking
pnpm lint                # Run Biome linting
pnpm lint:fix            # Run Biome linting with auto-fix
pnpm format              # Format code with Biome
```

## Database Commands

```bash
pnpm db:push             # Push schema changes to database
pnpm db:studio           # Open Prisma Studio UI
pnpm db:generate         # Generate Prisma client
pnpm db:migrate          # Run database migrations
pnpm db:seed             # Seed database with initial data
pnpm db:reset            # Reset database and run migrations
pnpm db:sync-users       # Sync Clerk users to database
```

## Test Commands

```bash
# Vitest Testing
pnpm test               # Run all tests in watch mode
pnpm test:run           # Run all tests once
pnpm test:unit          # Run only unit tests
pnpm test:unit:run      # Run unit tests once
pnpm test:coverage      # Generate test coverage report
pnpm test:ui            # Open Vitest UI
pnpm test:setup         # Setup test database

# Individual test execution
pnpm test auth-flows    # Run specific test file
pnpm test --grep "user creation"  # Run tests matching pattern
```

## Project Architecture

**BuddyChip** is an AI-powered Twitter mention monitoring and response tool. The project is structured as a **Turborepo monorepo** but currently contains a **single Next.js application** that serves both frontend and API functionality.

### Current Structure
- **`apps/web/`** - Single Next.js 15 application containing:
  - Frontend UI with shadcn/ui components
  - API routes for tRPC endpoints (`/api/trpc/[trpc]/route.ts`)
  - Database integration with Prisma ORM
  - Authentication via Clerk webhooks
- **`packages/`** - Empty (prepared for future shared packages)
- **`docs/`** - Documentation and specifications

### Core Application Features
1. **Twitter Mention Monitoring** - Track mentions of monitored accounts
2. **AI Response Generation** - Automated reply suggestions via "Benji" AI agent
3. **Quick Reply Tool** - Generate responses to any tweet
4. **Sentiment Analysis** - Bullish scoring (1-100) for tweets
5. **Subscription Management** - Multi-tier plans with feature limits

### Key Technologies
- **Framework**: Next.js 15 with React 19 and App Router
- **Database**: PostgreSQL with Prisma 6 ORM
- **API**: tRPC 11 for end-to-end type safety
- **Authentication**: Clerk with webhook integration
- **AI**: Vercel AI SDK v4 with multiple providers
- **UI**: shadcn/ui (New York style) with TailwindCSS 4
- **Build System**: Turborepo with PNPM workspaces
- **Code Quality**: Biome for linting and formatting
- **Testing**: Vitest with MSW for API mocking
- **Error Monitoring**: Sentry with session replay

## Important Configuration Details

### Database Setup
- Prisma schema is located at `apps/web/prisma/schema/schema.prisma` (custom location)
- Generated client outputs to `apps/web/prisma/generated/`
- Requires `DATABASE_URL` and `DIRECT_URL` environment variables
- Advanced indexing strategy for performance optimization

### tRPC API Architecture
- Main router in `apps/web/src/routers/index.ts` with sub-routers:
  - `benji` - AI agent operations
  - `user` - User management
  - `accounts` - Monitored Twitter accounts
  - `mentions` - Tweet mentions and sentiment analysis
  - `twitter` - Twitter API integration
- Client configuration in `apps/web/src/utils/trpc.ts`
- Rate limiting integrated with subscription plans

### AI Agent System ("Benji")
- **Framework**: Vercel AI SDK with streaming responses
- **Model Selection by Plan**:
  - Free: Gemini 2.5 Flash
  - Pro: Gemini 2.5 Pro + tools
  - Enterprise: OpenAI o1-mini + all tools
- **Available Tools**:
  - xAI Live Search (`xai-search.ts`) - Real-time web search
  - Exa Search (`exa-search.ts`) - Semantic knowledge search
  - OpenAI Image (`openai-image.ts`) - DALL-E image generation
- **Configuration**: Max 5 tool usage steps per conversation
- **Context**: Persistent conversation context via Mem0

### Subscription & Rate Limiting
- Feature-based subscription model with granular limits
- Rate limiting via Upstash KV store
- Usage tracking for billing periods
- Feature types: AI_CALLS, IMAGE_GENERATIONS, MONITORED_ACCOUNTS, etc.

### Authentication (Clerk)
- Full integration with frontend and backend components
- User sync webhook at `/api/webhooks/clerk`
- Automatic database synchronization
- Testing utilities for authentication mocking

### UI Component System
- shadcn/ui components with "new-york" style
- Custom components in `apps/web/src/components/`:
  - `ui/` - shadcn/ui base components
  - `landing/` - Marketing page components
  - `atoms/` - Custom base components
- Lottie animations for enhanced UX
- Dark/light theme support with next-themes

### Error Monitoring (Sentry)
- Separate projects for comprehensive error tracking
- Session replay enabled for debugging
- Environment-based configuration
- Error boundary component available

## Environment Variables

### Required (`apps/web/.env`)
```env
# Database
DATABASE_URL=postgresql://...
DIRECT_URL=postgresql://...

# Authentication
CLERK_SECRET_KEY=sk_...
CLERK_WEBHOOK_SIGNING_SECRET=whsec_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...

# AI Providers
OPENROUTER_API_KEY=sk-or-...
OPENAI_API_KEY=sk-...
XAI_API_KEY=xai-...
PERPLEXITY_API_KEY=pplx-...
EXA_API_KEY=...
COOKIE_API_KEY=...

# External Services
TWITTER_API_KEY=...
UPLOADTHING_TOKEN=...
MEM0_API_KEY=...

# Rate Limiting & Cache
KV_URL=...
KV_TOKEN=...

# Monitoring
SENTRY_AUTH_TOKEN=...

# Development Flags
VERBOSE_LOGGING=true
ENABLE_PRISMA_QUERY_LOGS=true
ENABLE_CONTEXT_LOGS=true
ENABLE_TRPC_REQUEST_LOGS=true

# API Configuration
OR_SITE_URL=https://...
OR_APP_NAME=BuddyChip
CORS_ORIGIN=http://localhost:3000
```

## Database Schema Key Models

### Core Data Models
- **User** - Clerk integration with subscription relationship
- **SubscriptionPlan** - Flexible feature-based pricing (Free/Pro/Enterprise)
- **PlanFeature** - Granular feature limits per plan
- **PersonalityProfile** - AI personality configurations for responses
- **MonitoredAccount** - Twitter accounts being tracked
- **Mention** - Tweet mentions with sentiment analysis and metadata
- **AIResponse** - Generated responses with quality metrics
- **Image** - UploadThing file storage integration
- **UsageLog** - Rate limiting and billing period tracking

### Important Relationships
- Users have subscription plans with feature limits
- Monitored accounts belong to users and generate mentions
- AI responses are generated for mentions with quality scoring
- Usage logs track feature consumption against plan limits

## Development Workflow

### Local Development
- Single Next.js app runs on default port (3000)
- Turbopack enabled for fast development builds
- Database changes pushed with `pnpm db:push`
- Real-time type safety with tRPC throughout the stack

### Testing Strategy
- **Unit Tests**: Individual component and utility testing
- **Integration Tests**: tRPC endpoint testing with MSW
- **Database Tests**: Isolated test database with cleanup
- **Authentication Tests**: Clerk testing utilities for mocking

### Code Quality
- **Biome**: Configured for consistent formatting and linting
- **TypeScript**: Strict mode enabled throughout
- **Error Handling**: Comprehensive error boundaries and monitoring
- **Performance**: Optimized database queries with advanced indexing

## Project-Specific Scripts

### Database Management
- `pnpm db:seed` - Initialize subscription plans and test data
- `pnpm db:sync-users` - Sync Clerk users to database
- `pnpm db:reset` - Complete database reset with fresh migrations

### Development Utilities
- `apps/web/src/scripts/fix-current-user.ts` - Fix user data inconsistencies
- `apps/web/src/scripts/seed-subscription-plans.ts` - Initialize subscription tiers

### External Service Integration
- Twitter API via twitterapi.io for mention fetching
- Multiple AI providers with fallback strategies
- UploadThing for secure file uploads
- Upstash KV for high-performance rate limiting

## CRITICAL SECURITY RULES

### Environment Variables & Secrets
**NEVER commit environment variables, API keys, tokens, or any secrets to the codebase:**
- ❌ Do NOT include secrets in committed files (`.js`, `.ts`, `.json`, etc.)
- ✅ Use `.env` files (which are gitignored) for local development
- ✅ Use environment variable injection for production deployments
- ✅ Always validate that secrets are loaded from `process.env`
- ✅ Keep `.env.example` with dummy values for documentation

### Database Migration Memory
- Every time we do a database migration, save the migration file in `/docs/prisma/` folder for historical tracking

## Development Guidelines

### Code Development Principles
- **NEVER TAKE SHORTCUTS, ALWAYS USE PRODUCTION READY SOLUTIONS, NEVER ADD MOCK DATAS**